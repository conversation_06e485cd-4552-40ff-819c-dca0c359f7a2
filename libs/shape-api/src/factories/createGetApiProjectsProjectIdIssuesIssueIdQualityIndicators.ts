// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsPathParamsSchema,
  GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponseSchema,
} from '../types/getApiProjectsProjectIdIssuesIssueIdQualityIndicatorsSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createIssueQualityIndicators } from './createIssueQualityIndicators';
import { faker } from '@faker-js/faker';

export function createGetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsPathParams(
  data?: Partial<GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsPathParamsSchema>
): GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsPathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid(), issue_id: faker.string.uuid() },
    ...(data || {}),
  };
}

/**
 * @description Quality indicators
 */
export function createGetApiProjectsProjectIdIssuesIssueIdQualityIndicators200() {
  faker.seed([100]);
  return createIssueQualityIndicators();
}

/**
 * @description Authentication required
 */
export function createGetApiProjectsProjectIdIssuesIssueIdQualityIndicators401() {
  faker.seed([100]);
  return createAuthenticationError();
}

/**
 * @description Not authorized
 */
export function createGetApiProjectsProjectIdIssuesIssueIdQualityIndicators403() {
  faker.seed([100]);
  return undefined;
}

/**
 * @description Not found
 */
export function createGetApiProjectsProjectIdIssuesIssueIdQualityIndicators404() {
  faker.seed([100]);
  return undefined;
}

export function createGetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponse(
  data?: Partial<GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponseSchema>
): GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponseSchema {
  faker.seed([100]);
  return data || faker.helpers.arrayElement<any>([createGetApiProjectsProjectIdIssuesIssueIdQualityIndicators200()]);
}
