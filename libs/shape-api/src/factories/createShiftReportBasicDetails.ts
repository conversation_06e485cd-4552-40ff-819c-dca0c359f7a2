// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ShiftReportBasicDetailsSchema } from '../types/shiftReportBasicDetailsSchema';
import { faker } from '@faker-js/faker';

export function createShiftReportBasicDetails(
  data?: Partial<ShiftReportBasicDetailsSchema>
): ShiftReportBasicDetailsSchema {
  faker.seed([100]);
  return {
    ...{
      id: faker.string.uuid(),
      approverId: faker.number.int(),
      archived: faker.datatype.boolean(),
      availableActions: {
        archive: faker.datatype.boolean(),
        delete: faker.datatype.boolean(),
        edit: faker.datatype.boolean(),
        editRootFields: faker.datatype.boolean(),
        export: faker.datatype.boolean(),
        listCollaboratorsComments: faker.datatype.boolean(),
        listPublicComments: faker.datatype.boolean(),
        publish: faker.datatype.boolean(),
        restore: faker.datatype.boolean(),
        submitForReview: faker.datatype.boolean(),
      },
      collaboratorsTeamMemberIds: faker.helpers.multiple(() => faker.number.int()),
      completionQualityScore: faker.number.int(),
      contractorName: faker.string.alpha(),
      createdAt: faker.date.anytime().toISOString(),
      documentCount: faker.number.int(),
      projectId: faker.string.uuid(),
      publishedAt: faker.date.anytime().toISOString(),
      reportDate: faker.date.anytime().toISOString().substring(0, 10),
      reportTitle: faker.string.alpha(),
      shiftType: faker.string.alpha(),
      state: faker.helpers.arrayElement<NonNullable<ShiftReportBasicDetailsSchema>['state']>([
        'draft',
        'in_review',
        'published',
      ]),
      teamId: faker.string.uuid(),
      teamMemberId: faker.number.int(),
    },
    ...(data || {}),
  };
}
