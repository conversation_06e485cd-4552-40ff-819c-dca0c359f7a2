// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ChangeSignalDowntimeDetailsBasicSchema } from '../types/changeSignalDowntimeDetailsBasicSchema';
import { faker } from '@faker-js/faker';

export function createChangeSignalDowntimeDetailsBasic(
  data?: Partial<ChangeSignalDowntimeDetailsBasicSchema>
): ChangeSignalDowntimeDetailsBasicSchema {
  faker.seed([100]);
  return {
    ...{
      id: faker.string.uuid(),
      locationId: faker.string.uuid(),
      publishedAt: faker.date.anytime().toISOString(),
      shiftReportId: faker.string.uuid(),
      signalId: faker.string.uuid(),
      signalType: faker.helpers.arrayElement<NonNullable<ChangeSignalDowntimeDetailsBasicSchema>['signalType']>([
        'downtime',
      ]),
      teamMemberId: faker.number.int(),
      title: faker.string.alpha(),
    },
    ...(data || {}),
  };
}
