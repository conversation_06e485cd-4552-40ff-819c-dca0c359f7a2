// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ChangeSignalIssueDetailsBasicSchema } from '../types/changeSignalIssueDetailsBasicSchema';
import { createIssueImpact } from './createIssueImpact';
import { faker } from '@faker-js/faker';

export function createChangeSignalIssueDetailsBasic(
  data?: Partial<ChangeSignalIssueDetailsBasicSchema>
): ChangeSignalIssueDetailsBasicSchema {
  faker.seed([100]);
  return {
    ...{
      id: faker.string.uuid(),
      impact: Object.assign({}, createIssueImpact()),
      locationId: faker.string.uuid(),
      publishedAt: faker.date.anytime().toISOString(),
      signalId: faker.string.uuid(),
      signalType: faker.helpers.arrayElement<NonNullable<ChangeSignalIssueDetailsBasicSchema>['signalType']>(['issue']),
      teamMemberId: faker.number.int(),
      title: faker.string.alpha(),
    },
    ...(data || {}),
  };
}
