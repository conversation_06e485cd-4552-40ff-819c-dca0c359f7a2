// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  PostApiProjectsProjectIdControlCenterPotentialChangesPathParamsSchema,
  PostApiProjectsProjectIdControlCenterPotentialChangesMutationRequestSchema,
  PostApiProjectsProjectIdControlCenterPotentialChangesMutationResponseSchema,
} from '../types/postApiProjectsProjectIdControlCenterPotentialChangesSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createError } from './createError';
import { createPotentialChange } from './createPotentialChange';
import { createPotentialChangeCategory } from './createPotentialChangeCategory';
import { createPotentialChangeEstimatedCostImpact } from './createPotentialChangeEstimatedCostImpact';
import { createPotentialChangeEstimatedScheduleImpact } from './createPotentialChangeEstimatedScheduleImpact';
import { createPotentialChangePriority } from './createPotentialChangePriority';
import { createPotentialChangeStatus } from './createPotentialChangeStatus';
import { faker } from '@faker-js/faker';

export function createPostApiProjectsProjectIdControlCenterPotentialChangesPathParams(
  data?: Partial<PostApiProjectsProjectIdControlCenterPotentialChangesPathParamsSchema>
): PostApiProjectsProjectIdControlCenterPotentialChangesPathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid() },
    ...(data || {}),
  };
}

/**
 * @description Potential change created
 */
export function createPostApiProjectsProjectIdControlCenterPotentialChanges201() {
  faker.seed([100]);
  return createPotentialChange();
}

/**
 * @description Authentication required
 */
export function createPostApiProjectsProjectIdControlCenterPotentialChanges401() {
  faker.seed([100]);
  return createAuthenticationError();
}

/**
 * @description Project not found
 */
export function createPostApiProjectsProjectIdControlCenterPotentialChanges404() {
  faker.seed([100]);
  return undefined;
}

/**
 * @description Invalid request
 */
export function createPostApiProjectsProjectIdControlCenterPotentialChanges422() {
  faker.seed([100]);
  return createError();
}

export function createPostApiProjectsProjectIdControlCenterPotentialChangesMutationRequest(
  data?: Partial<PostApiProjectsProjectIdControlCenterPotentialChangesMutationRequestSchema>
): PostApiProjectsProjectIdControlCenterPotentialChangesMutationRequestSchema {
  faker.seed([100]);
  return {
    ...{
      category: createPotentialChangeCategory(),
      comment: faker.string.alpha(),
      cost: faker.string.alpha(),
      early_warning_notice_submitted: faker.datatype.boolean(),
      estimated_cost_impact: createPotentialChangeEstimatedCostImpact(),
      estimated_schedule_impact: createPotentialChangeEstimatedScheduleImpact(),
      priority: createPotentialChangePriority(),
      status: createPotentialChangeStatus(),
      title: faker.string.alpha(),
      working_days_of_delay: faker.number.float(),
    },
    ...(data || {}),
  };
}

export function createPostApiProjectsProjectIdControlCenterPotentialChangesMutationResponse(
  data?: Partial<PostApiProjectsProjectIdControlCenterPotentialChangesMutationResponseSchema>
): PostApiProjectsProjectIdControlCenterPotentialChangesMutationResponseSchema {
  faker.seed([100]);
  return data || faker.helpers.arrayElement<any>([createPostApiProjectsProjectIdControlCenterPotentialChanges201()]);
}
