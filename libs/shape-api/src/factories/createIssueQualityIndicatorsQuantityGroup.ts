// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueQualityIndicatorsQuantityGroupSchema } from '../types/issueQualityIndicatorsQuantityGroupSchema';
import { faker } from '@faker-js/faker';

export function createIssueQualityIndicatorsQuantityGroup(
  data?: Partial<IssueQualityIndicatorsQuantityGroupSchema>
): IssueQualityIndicatorsQuantityGroupSchema {
  faker.seed([100]);
  return {
    ...{ total: faker.number.float(), completed: faker.number.float() },
    ...(data || {}),
  };
}
