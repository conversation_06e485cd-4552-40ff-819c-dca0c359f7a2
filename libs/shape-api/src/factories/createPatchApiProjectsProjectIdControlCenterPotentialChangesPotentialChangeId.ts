// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdPathParamsSchema,
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponseSchema,
} from '../types/patchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createError } from './createError';
import { createPotentialChange } from './createPotentialChange';
import { createPotentialChangeCategory } from './createPotentialChangeCategory';
import { createPotentialChangeEstimatedCostImpact } from './createPotentialChangeEstimatedCostImpact';
import { createPotentialChangeEstimatedScheduleImpact } from './createPotentialChangeEstimatedScheduleImpact';
import { createPotentialChangePriority } from './createPotentialChangePriority';
import { createPotentialChangeStatus } from './createPotentialChangeStatus';
import { faker } from '@faker-js/faker';

export function createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdPathParams(
  data?: Partial<PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdPathParamsSchema>
): PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdPathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid(), potential_change_id: faker.string.uuid() },
    ...(data || {}),
  };
}

/**
 * @description Potential change updated
 */
export function createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId200() {
  faker.seed([100]);
  return createPotentialChange();
}

/**
 * @description Authentication required
 */
export function createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId401() {
  faker.seed([100]);
  return createAuthenticationError();
}

/**
 * @description Not authorised
 */
export function createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId403() {
  faker.seed([100]);
  return createError();
}

/**
 * @description Potential change not found
 */
export function createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId404() {
  faker.seed([100]);
  return undefined;
}

export function createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequest(
  data?: Partial<PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema>
): PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema {
  faker.seed([100]);
  return {
    ...{
      category: createPotentialChangeCategory(),
      comment: faker.string.alpha(),
      cost: faker.string.alpha(),
      early_warning_notice_submitted: faker.datatype.boolean(),
      estimated_cost_impact: createPotentialChangeEstimatedCostImpact(),
      estimated_schedule_impact: createPotentialChangeEstimatedScheduleImpact(),
      priority: createPotentialChangePriority(),
      status: createPotentialChangeStatus(),
      title: faker.string.alpha(),
      working_days_of_delay: faker.number.float(),
    },
    ...(data || {}),
  };
}

export function createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponse(
  data?: Partial<PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponseSchema>
): PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponseSchema {
  faker.seed([100]);
  return (
    data ||
    faker.helpers.arrayElement<any>([
      createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId200(),
    ])
  );
}
