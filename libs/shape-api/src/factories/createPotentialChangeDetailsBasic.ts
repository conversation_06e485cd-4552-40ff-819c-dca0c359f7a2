// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PotentialChangeDetailsBasicSchema } from '../types/potentialChangeDetailsBasicSchema';
import { createPotentialChangeCategory } from './createPotentialChangeCategory';
import { createPotentialChangeEstimatedCostImpact } from './createPotentialChangeEstimatedCostImpact';
import { createPotentialChangeEstimatedScheduleImpact } from './createPotentialChangeEstimatedScheduleImpact';
import { createPotentialChangePriority } from './createPotentialChangePriority';
import { createPotentialChangeStatus } from './createPotentialChangeStatus';
import { faker } from '@faker-js/faker';

export function createPotentialChangeDetailsBasic(
  data?: Partial<PotentialChangeDetailsBasicSchema>
): PotentialChangeDetailsBasicSchema {
  faker.seed([100]);
  return {
    ...{
      archived: faker.datatype.boolean(),
      availableActions: {
        archive: faker.datatype.boolean(),
        linkChangeSignals: faker.datatype.boolean(),
        unlinkChangeSignals: faker.datatype.boolean(),
      },
      category: Object.assign({}, createPotentialChangeCategory()),
      comment: faker.string.alpha(),
      cost: faker.string.alpha(),
      createdAt: faker.date.anytime().toISOString(),
      earlyWarningNoticeSubmitted: faker.datatype.boolean(),
      estimatedCostImpact: Object.assign({}, createPotentialChangeEstimatedCostImpact()),
      estimatedScheduleImpact: Object.assign({}, createPotentialChangeEstimatedScheduleImpact()),
      id: faker.string.uuid(),
      priority: Object.assign({}, createPotentialChangePriority()),
      signalsCount: faker.number.int(),
      status: createPotentialChangeStatus(),
      teamMemberId: faker.number.int(),
      title: faker.string.alpha(),
      workingDaysOfDelay: faker.number.float(),
    },
    ...(data || {}),
  };
}
