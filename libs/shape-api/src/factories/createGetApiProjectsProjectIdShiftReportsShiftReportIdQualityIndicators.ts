// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsPathParamsSchema,
  GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponseSchema,
} from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createShiftReportQualityIndicators } from './createShiftReportQualityIndicators';
import { faker } from '@faker-js/faker';

export function createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsPathParams(
  data?: Partial<GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsPathParamsSchema>
): GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsPathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid(), shift_report_id: faker.string.uuid() },
    ...(data || {}),
  };
}

/**
 * @description Quality indicators
 */
export function createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators200() {
  faker.seed([100]);
  return createShiftReportQualityIndicators();
}

/**
 * @description Authentication required
 */
export function createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators401() {
  faker.seed([100]);
  return createAuthenticationError();
}

/**
 * @description Not authorized
 */
export function createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators403() {
  faker.seed([100]);
  return undefined;
}

/**
 * @description Not found
 */
export function createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators404() {
  faker.seed([100]);
  return undefined;
}

export function createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponse(
  data?: Partial<GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponseSchema>
): GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponseSchema {
  faker.seed([100]);
  return (
    data ||
    faker.helpers.arrayElement<any>([createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators200()])
  );
}
