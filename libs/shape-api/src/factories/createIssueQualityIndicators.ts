// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueQualityIndicatorsSchema } from '../types/issueQualityIndicatorsSchema';
import { createIssueQualityIndicatorsQuantityGroup } from './createIssueQualityIndicatorsQuantityGroup';
import { faker } from '@faker-js/faker';

export function createIssueQualityIndicators(
  data?: Partial<IssueQualityIndicatorsSchema>
): IssueQualityIndicatorsSchema {
  faker.seed([100]);
  return {
    ...{
      attachments: { percentage: createIssueQualityIndicatorsQuantityGroup() },
      basics: {
        items: {
          description: faker.datatype.boolean(),
          resolvedOrRecent: faker.datatype.boolean(),
          responsiblePerson: faker.datatype.boolean(),
        },
        percentage: createIssueQualityIndicatorsQuantityGroup(),
      },
      category: { percentage: createIssueQualityIndicatorsQuantityGroup() },
      currentScore: { percentage: createIssueQualityIndicatorsQuantityGroup() },
      discipline: { percentage: createIssueQualityIndicatorsQuantityGroup() },
      dueDate: { percentage: createIssueQualityIndicatorsQuantityGroup() },
      impact: { percentage: createIssueQualityIndicatorsQuantityGroup() },
      location: { percentage: createIssueQualityIndicatorsQuantityGroup() },
      staleness: { percentage: createIssueQualityIndicatorsQuantityGroup() },
    },
    ...(data || {}),
  };
}
