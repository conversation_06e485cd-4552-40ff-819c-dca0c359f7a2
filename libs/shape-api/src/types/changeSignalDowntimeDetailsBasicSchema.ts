/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const changeSignalDowntimeDetailsBasicSignalTypeEnum = {
  downtime: 'downtime',
} as const;

export type ChangeSignalDowntimeDetailsBasicSignalTypeEnumSchema =
  (typeof changeSignalDowntimeDetailsBasicSignalTypeEnum)[keyof typeof changeSignalDowntimeDetailsBasicSignalTypeEnum];

export type ChangeSignalDowntimeDetailsBasicSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string, uuid
   */
  locationId: string | null;
  /**
   * @type string, date-time
   */
  publishedAt: string;
  /**
   * @type string, uuid
   */
  shiftReportId: string;
  /**
   * @deprecated
   * @type string | undefined, uuid
   */
  signalId?: string;
  /**
   * @type string
   */
  signalType: ChangeSignalDowntimeDetailsBasicSignalTypeEnumSchema;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type string
   */
  title: string | null;
};
