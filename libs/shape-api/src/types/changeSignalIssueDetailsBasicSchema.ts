/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueImpactSchema } from './issueImpactSchema';

export const changeSignalIssueDetailsBasicSignalTypeEnum = {
  issue: 'issue',
} as const;

export type ChangeSignalIssueDetailsBasicSignalTypeEnumSchema =
  (typeof changeSignalIssueDetailsBasicSignalTypeEnum)[keyof typeof changeSignalIssueDetailsBasicSignalTypeEnum];

export type ChangeSignalIssueDetailsBasicSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  impact: IssueImpactSchema | null;
  /**
   * @type string, uuid
   */
  locationId: string | null;
  /**
   * @type string, date-time
   */
  publishedAt: string;
  /**
   * @deprecated
   * @type string | undefined, uuid
   */
  signalId?: string;
  /**
   * @type string
   */
  signalType: ChangeSignalIssueDetailsBasicSignalTypeEnumSchema;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type string
   */
  title: string | null;
};
