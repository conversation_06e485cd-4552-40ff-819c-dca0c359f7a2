/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const changeSignalsChangeSignalTypeEnum = {
  issue: 'issue',
  downtime: 'downtime',
} as const;

export type ChangeSignalsChangeSignalTypeEnumSchema =
  (typeof changeSignalsChangeSignalTypeEnum)[keyof typeof changeSignalsChangeSignalTypeEnum];

export type ChangeSignalsBodyParameterSchema = {
  /**
   * @type array
   */
  change_signals: {
    /**
     * @type string | undefined
     */
    change_signal_type?: ChangeSignalsChangeSignalTypeEnumSchema;
    /**
     * @type string | undefined, uuid
     */
    change_signal_id?: string;
    /**
     * @type string, uuid
     */
    id: string;
  }[];
};
