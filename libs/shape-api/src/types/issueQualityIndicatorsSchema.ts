/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueQualityIndicatorsQuantityGroupSchema } from './issueQualityIndicatorsQuantityGroupSchema';

export type IssueQualityIndicatorsSchema = {
  /**
   * @type object
   */
  attachments: {
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  basics: {
    /**
     * @type object
     */
    items: {
      /**
       * @type boolean
       */
      description: boolean;
      /**
       * @type boolean
       */
      resolvedOrRecent: boolean;
      /**
       * @type boolean
       */
      responsiblePerson: boolean;
    };
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  category: {
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  currentScore: {
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  discipline: {
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  dueDate: {
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  impact: {
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  location: {
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  staleness: {
    /**
     * @type object
     */
    percentage: IssueQualityIndicatorsQuantityGroupSchema;
  };
};
