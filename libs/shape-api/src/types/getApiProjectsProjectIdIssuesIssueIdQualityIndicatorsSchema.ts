/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { IssueQualityIndicatorsSchema } from './issueQualityIndicatorsSchema';

export type GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Quality indicators
 */
export type GetApiProjectsProjectIdIssuesIssueIdQualityIndicators200Schema = IssueQualityIndicatorsSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueIdQualityIndicators401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdIssuesIssueIdQualityIndicators403Schema = void;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdIssuesIssueIdQualityIndicators404Schema = void;

export type GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponseSchema =
  GetApiProjectsProjectIdIssuesIssueIdQualityIndicators200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueIdQualityIndicators200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueIdQualityIndicators401Schema
    | GetApiProjectsProjectIdIssuesIssueIdQualityIndicators403Schema
    | GetApiProjectsProjectIdIssuesIssueIdQualityIndicators404Schema;
};
