/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftReportQualityIndicatorsSchema } from './shiftReportQualityIndicatorsSchema';

export type GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Quality indicators
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators200Schema =
  ShiftReportQualityIndicatorsSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators403Schema = void;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators404Schema = void;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators200Schema;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators401Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators403Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators404Schema;
};
