import React from 'react';
import { render, screen } from '@testing-library/react';
import {
  ProgressBarBadge,
  ProgressBarBadgeTheme,
  ProgressBarHeader,
  ProgressBarRoot,
  ProgressBarSubtitle,
  ProgressBarTitle,
} from './ProgressBar';

describe('ProgressBar', () => {
  it('renders correctly', () => {
    render(<ProgressBarRoot progress={50} />);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toHaveAttribute('aria-valuenow', '50');
  });

  describe('when the title and subtitle is defined', () => {
    it('renders with the title and subtitle', () => {
      render(
        <ProgressBarRoot progress={40}>
          <ProgressBarHeader>
            <ProgressBarTitle>Progress</ProgressBarTitle>
            <ProgressBarSubtitle>Progress subtitle</ProgressBarSubtitle>
          </ProgressBarHeader>
        </ProgressBarRoot>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('Progress')).toBeInTheDocument();
      expect(screen.getByText('Progress subtitle')).toBeInTheDocument();
    });
  });

  describe('when the showProgress is true on the header', () => {
    it('renders the progress value', () => {
      render(
        <ProgressBarRoot progress={40}>
          <ProgressBarHeader showProgress />
        </ProgressBarRoot>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('40%')).toBeInTheDocument();
    });
  });

  describe('when a badge is defined', () => {
    it('renders the badge', () => {
      render(
        <ProgressBarRoot progress={40}>
          <ProgressBarHeader>
            <ProgressBarTitle>
              Example
              <ProgressBarBadge theme={ProgressBarBadgeTheme.BLUE} label="This is my badge" />
            </ProgressBarTitle>
            <ProgressBarSubtitle>
              Example
              <ProgressBarBadge theme={ProgressBarBadgeTheme.BLUE} label="This is my other badge" />
            </ProgressBarSubtitle>
          </ProgressBarHeader>
        </ProgressBarRoot>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('This is my badge')).toBeInTheDocument();
      expect(screen.getByText('This is my other badge')).toBeInTheDocument();
    });
  });

  describe('when disabled', () => {
    it('renders as disabled', () => {
      render(<ProgressBarRoot progress={40} disabled />);

      expect(screen.getByRole('progressbar')).toHaveAttribute('aria-disabled', 'true');
    });
  });

  describe('when variant is donut', () => {
    it('renders the donut progress component', () => {
      render(<ProgressBarRoot progress={50} variant="donut" />);

      expect(screen.getByText('50%')).toBeInTheDocument();
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument(); // No radix progressbar for donut
    });

    it('does not show progress text for small size', () => {
      render(<ProgressBarRoot progress={50} variant="donut" size="small" />);

      expect(screen.queryByText('50%')).not.toBeInTheDocument();
    });

    it('shows progress text for medium and large sizes', () => {
      const { rerender } = render(<ProgressBarRoot progress={50} variant="donut" size="medium" />);
      expect(screen.getByText('50%')).toBeInTheDocument();

      rerender(<ProgressBarRoot progress={75} variant="donut" size="large" />);
      expect(screen.getByText('75%')).toBeInTheDocument();
    });

    it('renders with title and subtitle', () => {
      render(
        <ProgressBarRoot progress={60} variant="donut">
          <ProgressBarHeader>
            <ProgressBarTitle>Donut Progress</ProgressBarTitle>
            <ProgressBarSubtitle>Donut subtitle</ProgressBarSubtitle>
          </ProgressBarHeader>
        </ProgressBarRoot>
      );

      expect(screen.getByText('Donut Progress')).toBeInTheDocument();
      expect(screen.getByText('Donut subtitle')).toBeInTheDocument();
      expect(screen.getByText('60%')).toBeInTheDocument();
    });

    it('applies different colors correctly', () => {
      const { rerender } = render(<ProgressBarRoot progress={50} variant="donut" color="success" />);

      // Check if SVG is rendered (we can't easily test stroke color in jsdom)
      expect(document.querySelector('svg')).toBeInTheDocument();
      expect(document.querySelector('circle')).toBeInTheDocument();

      rerender(<ProgressBarRoot progress={50} variant="donut" color="danger" />);
      expect(document.querySelector('svg')).toBeInTheDocument();
    });
  });
});
