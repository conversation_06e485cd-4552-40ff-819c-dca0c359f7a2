meta:
  title: Shape
  description: Shape is a place where construction organisations can create, track and resolve issues seamlessly on mobile and desktop. Organisations using Shape collaborate more effectively and close-out defects and safety issues faster and smarter!

state:
  loading: &stateLoading Loading

actions:
  continue: &actionContinue Continue
  archive: &actionArchive Archive
  cancel: &actionCancel Cancel
  create: &actionCreate Create
  delete: &actionDelete Delete
  dismiss: &action<PERSON><PERSON><PERSON>
  done: &actionDone Done
  duplicate: &actionDuplicate Duplicate
  edit: &actionEdit Edit
  next: &actionNext Next
  ok: &actionOk Ok
  yes: &actionYes Yes
  restore: &actionRestore Restore
  save: &actionSave Save
  update: &actionUpdate Update
  apply: &actionApply Apply
  unlink: &actionUnlink Unlink
  link: &actionLink Link
  export: &actionExport Export
  seeMore: See more
  seeLess: See less

admin:
  accessRequests:
    title: Access requests
    list:
      noPendingRequests: There are no pending access requests for <b>{displayName}</b>.
      hasPendingRequests: "You have the following access requests for <b>{displayName}</b> waiting to be reviewed:"
      teamRequested: Organisation requested
      destinationTeam: Destination organisation
    reallocate:
      title: Reallocate access request
      subtitle: Select a team to reallocate organisation request from <b>{userName}</b>.
      selectPlaceholder: Destination team *
      cancelCTA: Cancel
      confirmCTA: Reallocate
    review:
      title: Review access request
      routed: This request has been routed to <b>{team}</b> organisation automatically.
      redirected: This request has been routed to <b>{team}</b>.
      organisationRequested: Organisation requested
      message: Message
      noMessage: No message was left.
      reRoutedFrom: Re-routed from <b>{team}</b> admin
      defineRolesAndPermissions: "Define roles and permissions:"
      constructionRole:
        placeholder: Select a construction role
      permissionRole:
        placeholder: Select permissions
      differentTeam:
        caption: If the person is part of another organisation on this project, you can route the request to the admins of that organisation by clicking 'Different organisation'.
        switchTeamCTA: Different organisation
      cancelCTA: Reject
      confirmCTA: Accept
    reject:
      title: Reject access request
      subtitle: Are you sure you want to reject <b>{userName}&apos;s</b> request to access <b>{teamName}</b>?
      cancelCTA: Cancel
      confirmCTA: Reject
    accept:
      title: Accept access request
      subtitle: "If you accept this request you will grant <b>{userName}</b> access to <b>{teamName}</b>."
      alert: Sending new invites implies billing changes
      cancelCTA: Cancel
      confirmCTA: Accept
      billing:
        warning: &billingWarning Sending new invites implies billing changes.
        info: &billingInfo "You are inviting { COUNT, plural, one {# new user} other {# new users}} which will imply the following price changes in your billing:"
        newLicense:
          term: &billingNewLicenseTerm New licenses
          details: &billingNewLicenseDetails "{pricePerLicense} x {numberOfLicenses} Shape users { numberOfLicenses, plural, one {license} other {licenses}}"

  customFields:
    title: &customFieldsTitle Custom fields
    newButton: &newCustomFieldButton New field
    form:
      newTitle: New custom field
      editTitle: Update custom field
      fieldTitle:
        label: Field title
        characters: characters
        errors:
          required: Required field
          length: Field title too long
      visibility:
        label: Visibility
        description: Only the principal organisation can add project wide fields.
        options:
          projectWide:
            label: Project wide ({actualNumber} of {maximumNumber} in use)
            description: Custom fields that are used by and visible to all organisations on the project.
          team:
            label: Organisation ({actualNumber} of {maximumNumber} in use)
            description: Custom fields that are only used by and visible to your organisation.
            current: current organisation
        errors:
          maximumReached: "You hit the limit and can't create more {
            type, select,
            projectWide {project wide custom fields}
            team {organisation custom fields}
            other {custom fields}
            }."
          unableToCreate: You hit the limit and can't create more custom fields.
    table:
      title: Title
      visibility: Visibility
      projectWide: Project wide
      team: Organisation
      maximumInfo: There is a limit of {maximumProjectWide} project wide and {maximumTeam} organisation custom fields.
      actions:
        header: Actions
        edit: Edit custom field
        remove: Remove custom field
    delete:
      title: Remove custom field
      subTitle: You are about to remove {customFieldLabel} field. After this step, {customFieldLabel}, will be removed from all issues completely and you can't undo it.
      cancel: *actionCancel
      remove: Remove
    empty:
      title: You don't have custom fields yet!
      subTitle: Get started by creating one.
      actionLabel: *newCustomFieldButton

  locations:
    actions:
      cancel: *actionCancel
      create: *actionCreate
      delete: *actionDelete
      duplicate: *actionDuplicate
      deleteSelected: Delete selected
      edit: *actionEdit
      ok: *actionOk
      update: *actionUpdate
      collapseAll: Collapse all
      expandAll: Expand all
      selectAllCTA: Select all
      unselectCTA: "Unselect"
      moveUp: "Move up"
      moveDown: "Move down"
    delete:
      confirmDeleteLocationOverlay:
        title: Delete location
        subTitle: Are you sure you want to delete this location? This action cannot be undone.
        cancelCTA: *actionCancel
        deleteCTA: *actionDelete
      locationHasChildren:
        title: This location is in use and cannot be deleted.
        subTitle: The location has associated sublocations. To allow deletion of this location, sublocations will need to be unlinked from it first.
        close: Close
      locationHasDocuments:
        title: This location is in use and cannot be deleted.
        subTitle: The location has associated documents. To allow deletion of this location, documents will need to be unlinked from it first.
        close: Close
      locationHasIssues:
        title: This location is in use and cannot be deleted.
        subTitle: The location has associated issues. To allow deletion of this location, issues will need to be unlinked from it first.
        close: Close
      locationHasShiftReports:
        title: This location is in use and cannot be deleted.
        subTitle: The location has associated shift reports. To allow deletion of this shift reports, issues will need to be unlinked from it first.
        close: Close
      deleteLocationErrorOverlay:
        title: Unable to delete
        subTitle: There was an error while trying to delete this item, please try again.
        dismissCTA: *actionDismiss
    form:
      addTitle: Add location
      editTitle: Edit location
      location: Location name
      shortCode: Short code
      addCTA: Add
      editCTA: Update
      cancelCTA: Cancel
      errors:
        required: Required field
    selected: "{ selectedNum, plural, one {# location} other {# locations}} selected"
    title: Locations
  disciplines:
    title: Disciplines
    selected: "{ selectedNum, plural, one {# discipline} other {# disciplines}} selected"
    actions:
      cancel: *actionCancel
      create: *actionCreate
      delete: *actionDelete
      duplicate: *actionDuplicate
      deleteSelected: Delete selected
      edit: *actionEdit
      ok: *actionOk
      update: *actionUpdate
      collapseAll: Collapse all
      expandAll: Expand all
      selectAllCTA: Select all
      unselectCTA: "Unselect"
      moveUp: "Move up"
      moveDown: "Move down"
    form:
      addTitle: Add discipline
      editTitle: Edit discipline
      discipline: Discipline name
      shortCode: Short code
      addCTA: Add
      editCTA: Update
      cancelCTA: Cancel
      errors:
        required: Required field
    delete:
      confirmDeleteDisciplineOverlay:
        title: Delete discipline
        subTitle: Are you sure you want to delete this discipline? This action cannot be undone.
        cancelCTA: *actionCancel
        deleteCTA: *actionDelete
      disciplineHasChildren:
        title: This discipline is in use and cannot be deleted.
        subTitle: The discipline has associated subdisciplines. To allow deletion of this discipline, subdisciplines will need to be unlinked from it first.
        close: Close
      disciplineHasIssues:
        title: This discipline is in use and cannot be deleted.
        subTitle: The discipline has associated issues. To allow deletion of this discipline, issues will need to be unlinked from it first.
        close: Close
      deleteDisciplineErrorOverlay:
        title: Unable to delete
        subTitle: There was an error while trying to delete this item, please try again.
        dismissCTA: *actionDismiss

  resources:
    title: Resources
    person:
      title: People
      nameColumn: Person name
      emptyState:
        title: No people
      createResourceButtonLabel: New person
      createResourceModal:
        title: New person
        nameInputLabel: Person name
        nameInputPlaceholder: Person name
        successMessage: New person was added successfully
    organisation:
      title: Organisations
      nameColumn: Organisation name
      emptyState:
        title: No organisations
      createResourceButtonLabel: New organisation
      createResourceModal:
        title: New organisation
        nameInputLabel: Organisation name
        nameInputPlaceholder: Organisation name
        successMessage: New organisation was added successfully
    role:
      title: Roles
      nameColumn: Role name
      emptyState:
        title: No roles
      createResourceButtonLabel: New role
      createResourceModal:
        title: New role
        nameInputLabel: Role name
        nameInputPlaceholder: Role name
        successMessage: New role was added successfully
    equipment:
      title: Equipment
      nameColumn: Equipment name
      emptyState:
        title: No equipment
      createResourceButtonLabel: New equipment
      createResourceModal:
        title: New equipment
        nameInputLabel: Equipment name
        nameInputPlaceholder: Equipment name
        successMessage: New equipment was added successfully
    material:
      title: Material
      nameColumn: Material name
      emptyState:
        title: No materials
      createResourceButtonLabel: New material
      createResourceModal:
        title: New material
        nameInputLabel: Material name
        nameInputPlaceholder: Material name
        successMessage: New material was added successfully
    dropdown:
      disable:
        title: Disable
        subtitle: You will not see this resource option on new shift reports.
        toast: '"{resourceName}" was disabled and will be removed as a resource option in new shift reports.'
      enable:
        title: Enable
        subtitle: You will see this resource option on new shift reports.
        toast: '"{resourceName}" was enabled and can now be used in new shift reports.'
    status:
      nameColumn: Status
      tooltip: Disabled resources can’t be selected in new shift reports.
      enabled: Enabled
      disabled: Disabled

  project:
    addUsers: Add users
    invite:
      title: Add users
      form:
        input: Email
        addEmail: Add
        footer:
          back: Back
          cancel: Cancel
          invite: Invite
          next: Next
        billing:
          warning: *billingWarning
          info: *billingInfo
          newLicense:
            term: *billingNewLicenseTerm
            details: *billingNewLicenseDetails
      success: The invitation was successfully sent.
      people:
        email: Email address
        permissions: Permissions
        role: Role
        empty: Type in email addresses above to add users to the invite list.

  projectSettings:
    title: Project settings
    projectDetails:
      title: Project details
      noPermissionSave: You don't have permission to edit project details
    archive:
      title: Archive project
      subtitle: Archiving this project will move it into your archived list where you can still access all of your data.
      actions:
        archive: Archive project
        noPermissionArchive: You don't have permission to archive this project

  teams:
    title: Organisations
    new: New organisation
    deleteTeam:
      cancelCTA: Cancel
      deleteCTA: Delete
      subtitle: "Are you sure you want to remove {teamName} from this project?"
      title: Delete a organisation
    list:
      membersPlaceholder: people
      actions:
        updateName: Edit name
        delete: "Delete"
    invite:
      header: New organisation
      step1: "Step 1: Set organisation name"
      step2: "Step 2: Add responsible people"
      emailLabel: Add the people responsible for this organisation by email address.
      emailPlaceholder: Email
      nameLabel: Give the organisation a name by which it will be known on this project.
      namePlaceholder: Display name
      actions:
        add: Add another organisation
        cancel: *actionCancel
        create: Create organisation
        done: *actionDone
        invite: Add and notify to join
    updateName:
      cancelCTA: Cancel
      updateCTA: Update
      teamName: Organisation name
      title: Edit organisation name
      errors:
        maxLength: The organisation name must be at most 40 characters
        minLength: The organisation name must be at least 3 characters
        required: Organisation name is required

    setup:
      header: Setup organisation
      step1: "Step 1: Set your organisation's name"
      step2: "Step 2: Add responsible people"
      emailLabel: Add the people responsible for this organisation by email address.
      nameLabel: Your project is created! It's time to set it up to collaborate with other people. Give your organisation a name by which it will be known on this project.
      actions:
        setName: Set name
        done: *actionDone
  people:
    remove:
      confirmCTA: Remove {name}
      cancelCTA: Cancel
      dependencies:
        description: "You are removing a person who is associated with open issues. To proceed, you must select someone to inherit these issues:"
        noneDescription: "{name} is not involved in any issues"
        showAllCTA: Show all issues
        showLessCTA: Show less
        sectionTitles:
          observedIssues: Observer
          assignmentRequestedIssues: Assigned (not accepted)
          assignedIssues: Assigned
          issuesAsApprover: Approver
      confirmModal:
        title: Remove user from project
        subTitle: This action is not reversible!
        cancelCTA: Cancel
        confirmCTA: Remove user
        license:
          term: Remove licenses
          details: *billingNewLicenseDetails
          description: Removing {name} means they will no longer have access to the project. You will no longer be billed for this user from the beginning of the next payment cycle.
    edit:
      title: Edit role and permissions
      labels:
        role: Permissions
        constructionRole: Role
  menu:
    customFields: *customFieldsTitle
    locations: Locations
    disciplines: Disciplines
    teams: Organisations
    accessRequests: Access requests
    resources: Resources
    projectSettings: Project settings

auth:
  page:
    title: Log in or create an account
    emailLabel: Email
    continueCTA: Continue
  forgotPassword:
    backToLogin:
      backTo: Back to
      login: login
    step1:
      title: Reset your password
      subtitle: Enter your email address to receive password reset instructions.
      resetPassword: Reset password
    step2:
      title: Change password
      subtitle: Enter a new password along with the code we've sent to
      enterSixDigitCode: Enter 6-digit code
      password: Password
      confirmPassword: Confirm password
      changePassword: Change password
      haventReceivedCode: Haven't received the code or it expired?
      resendCode: Resend verification code
      havingTrouble: Having trouble with changing your password?
      contactUs: Contact us
      verificationCodeResent: Verification code resent
      resetFailed: There was a problem resetting your password. Please try again!
    step3:
      title: New password set
      subtitle: You can now log in with your new password.
  confirmEmail:
    title: Enter verification code
    subtitle: To verify your email, enter the code we've sent to <b>{email}</b>.
    backToLogin:
      backTo: Back to
      loginLink: login
    resendEmail:
      question: Haven't received the code or it expired?
      resendButton: Resend verification code
    toast:
      success: Email verified.
    support: Having trouble with verifying your email?
    contactUs: Contact us
  login:
    title: Log in
    emailLabel: Email
    passwordLabel: Password
    submitCTA: Log in
    forgotYourPassword: Forgot your password?
    notYou: Not you?
  resetPassword:
    contactUs: Contact us
    support: Having trouble with resetting your password?
    successStatus:
      title: New password set
      subtitle: You can now log in with your new password.
    failureStatus:
      alertMessage: There was a problem resetting your password. Please try again!
    backToLogin:
      backTo: Back to
      loginLink: login
    expiredLink:
      title: Expired link
      subtitle: This reset password link has expired. You may generate a new one by clicking the button below.
      resendCTA: Resend link
  signup:
    createAccount:
      title: Create an account
      createCTA: Create account
      emailInputLabel: Email
      firstNameInputLabel: First name
      lastNameInputLabel: Last name
      passwordInputLabel: Password
    eua:
      statement: I've read and accept the
      euaLink: User Agreement
    backToLogin:
      hasAccount: Already have an account?
      loginLink: Log in
    notYou: Not you?
    google:
      title: You are creating an account using Google
    microsoft:
      title: You are creating an account using Microsoft
      continueWithCTA: Continue with Microsoft
  or: or
  passwordCheckList:
    min: At least 8 characters
    uppercase: Includes uppercase letter
    lowercase: Includes lowercase letter
    number: Contains number
    special: Contains special characters
    match_passwords: Passwords must match
  verificationSent:
    success: "New verification email sent."
    fail: "There was a problem resending the email, please try again."
  sso:
    title: Log in with Google or Microsoft
    subtitle: Your organisation requires Single Sign-On (SSO) for authentication. Please log in using your Google or Microsoft account to continue.
    backTo: Go back

channels:
  previewAvatar:
    personal: Personal avatar
    messaging: User avatar
    channel: Channel avatar
  actions:
    continue: *actionContinue
  channelInvitationGuide:
    title: How does this work?
    subTitle: Log in or create a Shape account to join this organisation.
    joinTeamDescription: Join {teamName} on Channels by 3 simple steps!
    contactSupport: Contact our support
    helpText: Need help? Visit our
    or: or
    faqUrl: https://help.shape.construction/hc/en-us/categories/**************-Channels
    faq: FAQ
    step1:
      title: Step 1
      description: Create a Shape account or log in if you have an existing one.
    step2:
      title: Step 2
      description: Accept the organisation’s invitation once you are logged in.
    step3:
      title: Step 3
      description: |
        Download and install Channels on your Android or Apple device.
        <br/><br/>
        Once installed, log in using the account you used to accept the organisation's invitation.
        <br/><br/>
        You can also scan the QR code below.
  events:
    delete: The group was deleted
  inviteDialog:
    description: You are currently logged-in as { email }
    differentAccount: Use a different account
    thisAccount: Join using this account
  invite:
    loading: &stateLoading Loading
    joining: Joining organisation...
    expired: This invitation link has already expired.
    description: You are invited to join this organisation on Channels
    storesTitle: Don't have Channels yet?
    storesSubTitle: Or continue exploring Shape
    webApp: Shape web app
    successfully: Successfully joined {teamName}
  channelsList:
    title: Channels web
    search: Search channel or member name
    autoTag: Auto
  details:
    actions:
      edit: Edit
      deleteGroup: Delete group
      addMembers:
        title: Add members
        description: Existing Channels members
    addMembers:
      title: Add members
      searchPlaceholder: Search
      alreadySelected: Already added to the group
      actions:
        done: *actionDone
      notifications:
        success: Members added successfully
        error: Error adding members
    edit:
      title: Edit group
      titlePlaceholder: Group name
      addPhoto: Add photo
      editPhoto: Edit photo
      success: Group updated successfully
      error: Error updating the group
      actions:
        cancel: *actionCancel
        save: Save changes
        removePhoto: Remove photo
    groupMemberOptions:
      removeFromGroup: Remove from group
      makeGroupModerator: Make a group moderator
      dismissModerator: Dismiss as moderator
      openMemberOptionsLabel: Open member options
    removeMembers:
      notifications:
        removeMemberSuccess: "{username} removed {member} from the group"
        removeMemberError: Failed to remove member
      removeMemberConfirmation:
        title: Remove member
        description: "Are you sure you want to remove {member} from this group?"
        actions:
          cancel: *actionCancel
          delete: *actionYes
    updateMembersRole:
      notifications:
        memberToModeratorSuccess: "{member} is now a moderator"
        memberNoLongerModeratorSuccess: "{member} is no longer a moderator"
        updateMembersRoleError: "Failed to update members role"
    channelType:
      team: Organisation
      group: Group
      messaging: Private
      personal: Personal
    deleteConfirmation:
      title: Delete for all
      subTitle: Do you want to delete the group and all its messages for everyone? This action cannot be undone.
      cancelCTA: *actionCancel
      deleteCTA: *actionDelete
      error: Error deleting the group
      success: Group deleted successfully
    members: "{count} members"
    moderator: Moderator
    mediaSection:
      title: Auto-upload media and documents to Shape gallery
      subtitle: If on, all files uploaded in this channel will automatically be added to Shape's gallery.
  channel:
    messageInput:
      placeholder: Send a message
      dragUploadAccepted: Drag your files here
      dragUploadRejected: Some of the files will not be accepted
      fileSizeError: The file "{filename}" should be greater than {min} bytes and smaller than {max} MB
    header:
      subtitle: Click here for conversation settings
      events:
        typing: "{ count, plural, one {{users, list} is typing...} other {{users, list} are typing...}}"
  newChannel:
    action: Create new channel
    title: Select a contact
    searchPlaceholder: Search
    errorMessages:
      creatingChannelError: Error on creating channel
    newGroup:
      action: New group
      membersStep:
        title: Add people
      infoStep:
        avatarPlaceholder: Change
        groupNamePlaceholder: Group name (optional)
        membersCount: "People: {count}"
        selectedUsers: Selected users
        title: Channel info
        uploadErrorMessage: The file selected has an unsupported extension or must be smaller than {max} MB
  messages:
    actions:
      copy: Copy message
      delete: Delete message
      edit: Edit message
      flag: Flag
      markUnread: Mark as unread
      mute: Mute
      pin: Pin
      reply: Reply
      saveToGallery: Save to Shape gallery
      unmute: Unmute
      unpin: Unipn
    copyMessage: Link copied to clipboard
    projectSelector:
      title: Select a project
    saveToGallery:
      success: Attachments saved to gallery

crashReport:
  title: Something went wrong
  cancelCTA: Cancel
  submitCTA: Submit
  description:
    label: Please tell us what happened
  subject: User crash report

currentUserLabel: (You)

dashboard:
  title: Dashboards
  tabs:
    dataHealth: Data health
    insights: Insights
  issuesStaleness:
    drillDown:
      active:
        title: Active issues
      idle:
        title: Idle issues
      stale:
        title: Stale issues
      empty:
        title: No issues found
        body: There are currently no issues that match this criteria.
        action: Refresh
    series:
      active:
        label: Active
        description: "Less than 2 weeks since the last activity"
      idle:
        label: Idle
        description: "More than 2 weeks since the last activity"
      stale:
        label: Stale
        description: "More than 4 weeks since the last activity"
    title: Stale issues
    tooltip:
      click: Click for more details
      issues: issues
  metabase:
    title: dashboard
  placeholder:
    title: New charts are coming soon
    body: What would you like to see here?
    contactUs: Contact us

dataBook:
  title: Data book
  description: Your go-to tool for transforming complex project data into clear, actionable insights. With powerful navigation and intuitive visualizations, it makes data accessible, meaningful, and ready to drive results.
  overview: Overview
  menu:
    home: Home
    shiftManager:
      title: Shift reports
    issueTracker:
      title: Issues
    customDashboard:
      title: Custom dashboards
  metabaseIframeTitle: Metabase Dashboard
  page:
    shiftManager:
      title: Shift reports
      description: Offers actionable insights into shift report quality and employee engagement. Enable your team to track report health, identify data gaps, and monitor hours allocation trends for better workforce planning and decision-making.
      insights:
        title: "{teamName} performance"
      otherInsights: Insights
    shiftReportsHeatmap:
      title: Heatmap
      description: See shift report quality and consistency over time.
    shiftReportDataQuality:
      title: Data quality
      description: Deep dive into shift report quality. See where data is missing.
    shiftReportResourceAllocation:
      title: Resource allocation
      description: Analyse plant, labour, and material utilisation.
    issueTracker:
      title: Issues
      description: Monitor issues' quality health, identify data gaps, track workforce trends, and analyze issues and delays across projects. Immediately see areas needing attention and showcasing good practices for better planning, accountability, and overall performance improvement.
      insights:
        title: "{teamName} performance"
    issuesHealthHeatmap:
      title: Heatmap
      description: See issues quality and consistency over time.
    issuesAnalytics:
      title: Analytics
      description: Issue tracking and data quality with open items and due date changes.
    issuesDelays:
      title: Delays
      description: Project performance highlighting delays to drive accountability.
    customDashboard:
      title: Custom dashboards
      insights: Insights
      badgeLabel: PRO
      unlockPage:
        title: Unlock custom dashboards - upgrade to pro
        description: Get full control with custom dashboards - track what matters most, your way.
        primaryButton: Upgrade to pro
        secondaryButton: Talk to us
      addMore:
        heading: Add more custom dashboard
        title: Create your own custom dashboard
        description: Tailor your dashboard to what matters most for you and your team.
        btnTitle: Talk to us
      email:
        subject: Custom dashboard request
        body: "Hello Shape team. I would like to create a custom dashboard for my organisation { teamName } [organisation id: { teamId }]."

    heatmapDashboard:
      heatmap:
        title: "{teamName} performance"
        navigateNextLabel: Next
        navigatePreviousLabel: Previous
        cell:
          label: Score
          popover:
            label: Health Score
            clickForDetails: Click for details
            noPublishedRecord: No published records
            publishedRecords: "Published records: <b>{recordCount}</b>"
            averageScore: "Average score: <b>{score}%</b>"
        axisLabels:
          to: to
        emptyState:
          title: No data available yet
          body: This chart will update automatically as users start submitting records. Invite your organisation members to Shape and start tracking their performance.
          inviteButton:
            label: Invite people
        qualityLabel:
          title: Completeness
          notUseful: Not useful
          theBasics: The basics
          good: Good
          veryGood: Very good
          comprehensive: Comprehensive
      performanceDetails:
        title: Performance detail
        shiftReportsTable:
          label: Shift reports
          reportTitle: Report {reportDate}
          headers:
            document: Document
            score: Score
            date: Date
            shift: Shift
            attachments: Attachments
          emptyState:
            title: No reports found
            body: This user hasn't submitted any reports for the selected time frame.
        issueReportsTable:
          label: Issues
          headers:
            title: Title
            currentResponsible: Current responsible
            score: Score
          emptyState:
            title: No reports found
            body: This user hasn't submitted any reports for the selected time frame.
      healthLevels:
        0:
          label: ""
        1:
          label: Not useful
        2:
          label: The basics
        3:
          label: Good
        4:
          label: Very good
        5:
          label: Comprehensive
  breadcrumbs:
    dataBook: Data book
    shiftManager: Shift reports
    issueTracker: Issues
    customDashboard: Custom dashboards
  actions:
    helpSection:
      title: Get to know more
      productTours:
        title: Product tours
        description: Explore shape through interactive tours & video guides.
        btnTitle: Explore tours
      helpCenter:
        title: Help center
        description: Self-service access to guides, FAQs, and support resources for Shape.
        btnTitle: Visit help center
      contactSupport:
        title: Contact support
        description: Need help? Reach out to our customer support for any assistance.
        btnTitle: Send as an email
    pageToolbar:
      filters: Filters
      needHelp: Need help?
      share: Share
      export: Export
  share:
    shareButton: Share
    popoverTitle: Share dashboard link

dataHealthDashboard:
  heatmap:
    title: "{teamName} performance"
    navigateNextLabel: Next
    navigatePreviousLabel: Previous
    cell:
      label: Score
      popover:
        label: Health Score
        clickForDetails: Click for details
        noPublishedRecord: No published records
        publishedRecords: "Published records: <b>{recordCount}</b>"
        averageScore: "Average score: <b>{score}%</b>"
    axisLabels:
      to: to
    emptyState:
      title: No data available yet
      body: This chart will update automatically as users start submitting records. Invite your organisation members to Shape and start tracking their performance.
      inviteButton:
        label: Invite people
  performanceDetails:
    title: Performance detail
    shiftReportsTable:
      label: Shift reports
      reportTitle: Report {reportDate}
      headers:
        document: Document
        score: Score
        date: Date
        shift: Shift
        attachments: Attachments
      next: Next
      previous: Previous
      emptyState:
        title: No reports found
        body: This user hasn't submitted any reports for the selected time frame.
  healthLevels:
    0:
      label: ""
    1:
      label: Not useful
    2:
      label: The basics
    3:
      label: Good
    4:
      label: Very good
    5:
      label: Comprehensive

dateTime:
  timezoneInfo: "Date/time must refer to <b>{timezone}</b>"
  dateShouldBeFuture: "The date should be in the future."

error:
  title: Something went wrong
  content: Try refreshing or go back to the project. If this problem persists, please
  contactCTA: contact us
  submitFeedbackCTA: Submit feedback
  goBackCTA: Go back to the project
  version: Shape version {version}
  file_too_big: 'The file "{filename}" exceeds the maximum allowed size of {max} MB'
  file_size_zero: 'The file "{filename}" needs to be greater than {min} bytes'
  uploadError: "Could not upload"

errors:
  characterLimit: "{fieldName} must be at most {maxLength} characters"
  requiredField: Required field
  invalidDate: Invalid date
  invalidTime: Invalid time
  fileUpload:
    fileTypeInvalid: '"{filename}" is not a .jpeg, .jpg or .png. Please upload in a supported format.'
    fileSizeMin: '"{filename}" needs to be at least {min} bytes. Please upload a larger file.'
    fileSizeMax: '"{filename}" cannot be more than {max} MB. Please upload a smaller file.'

eua:
  title: We've updated our End User Licence Agreement
  content: To continue using the Shape app, please read and accept the updated Agreement. This will take effect on {date}.
  acceptCTA: Accept
  loginCTA: Back to login
  readCTA: Read End User Licence Agreement
  modal:
    title: End User Licence Agreement
    date: Effective {date}

exports:
  toasts:
    loading: "Your {exportType} export is in progress. It will automatically download when it's ready."
    success: Export complete. If the download didn't start, please click the button below.
    failed: "Your {exportType} Export failed. Please try exporting again. If a problem arises, please check your internet connection."
  download: Download
  operationName:
    issue: issue
    issue_list: bulk issue
    potential_change: change
    shift_activity_list: shift activity
    shift_report: shift report
    shift_report_list: bulk shift report
    weekly_work_plan: weekly work plan
    weekly_work_plan_lookback: weekly work plan lookback

filters:
  toolbar:
    fields:
      label: Fields
    label: Filters
    clearCTA: Clear all
    dateSelectFilter:
      triggerLabel: Date
      filterByLabel: Select a date
      options:
        all: All
        today: Today
        yesterday: Yesterday
        tomorrow: Tomorrow
        thisWeek: This week
        thisMonth: This month
        sevenDaysAgo: Last 7 days
        thirtyDaysAgo: Last 30 days
        nextWeek: Next week
        nextTwoWeeks: Next 2 weeks
        nextFourWeeks: Next 4 weeks
        nextEightWeeks: Next 8 weeks
        custom: Custom
      plannedStart:
        triggerLabel: Planned start
        filterByLabel: Filter by planned start date
      plannedEnd:
        triggerLabel: Planned finish
        filterByLabel: Filter by planned finish date
      actualStart:
        triggerLabel: Actual start
        filterByLabel: Filter by actual start date
      actualEnd:
        triggerLabel: Actual finish
        filterByLabel: Filter by actual finish date
      expectedFinish:
        triggerLabel: Expected finish
        filterByLabel: Filter by expected finish date
    peopleSelectFilter:
      triggerLabel: Author
      responsibleTriggerLabel: Responsible
      filterByLabel: Filter by author
      responsibleFilterByLabel: Filter by responsible
      searchPlaceholder: Search for a person
    ownerSelectFilter:
      triggerLabel: Activity owner
      filterByLabel: Filter by activity owner
      searchPlaceholder: Search for an activity owner
    categorySelectFilter:
      triggerLabel: Category
      drawerTitle: Categories
      searchPlaceholder: Search for category / sub-category
      noCategory: No category / sub-category
    criticalSelectFilter:
      triggerLabel: Critical
      filterByLabel: Filter by critical
      options:
        all: All
        critical: Critical
        notCritical: Not critical
    disciplineSelectFilter:
      triggerLabel: Discipline
      drawerTitle: Disciplines
      searchPlaceholder: Search for discipline
      noDisciplineLabel: No discipline
    searchFilter:
      placeholder: Search
      activitiesPlaceholder: Search by name, reference or task ID
    statusSelectFilter:
      triggerLabel: Status
      filterByLabel: Filter by status
    locationSelectFilter:
      triggerLabel: Location
      drawerTitle: Locations
      searchPlaceholder: Search for a location
      noLocationLabel: No location
    resourcesSelectFilter:
      triggerLabel: Organisation
      filterByLabel: Filter by organisation
      searchPlaceholder: Search for an organisation
      organisationSuffix: organisations
    weeklyPlanSelectFilter:
      placeholder: Select work plan
      loading: Loading
      loadMore: Load more work plans
    selectAllFilter:
      selectNone: Select none
      selectAllVisible: Select all visible ({count} items)
      triggerLabel: "{count} selected"
    group:
      triggerLabel: Group by
      filterByLabel: Group by
      options:
        none: None
        responsible: Responsible
        location: Location
        organisation: Organisation

feedbackForm:
  title: Send feedback
  supportMessage: If you have any problem, please send an email to
  likeQuestion:
    question: What do you like about Shape?
  improveQuestion:
    question: What can we improve?
  cancelCTA: Cancel
  submitCTA: Submit
  successMessage: "Thanks. Feedback submitted."

helpCenter:
  title: Help & Tips
  learnMore: Learn more about Shape
  bookSession: Book a free setup session
  contact: Contact support
  goTo: Go to help center
  productTipsAlert:
    title: Product tips
    cta: See tips
  articles:
    title: Need some help?
    readMore: Read more
    draft: Draft
    emptyState:
      title: No entries found
      descriptionPart1: We couldn't find any articles related to the current page. You can visit our
      helpCenterLink: help center
      descriptionPart2: and browse available articles, or you can
      contactSupportLink: contact support
      descriptionPart3: directly.

imageMarkupEditor:
  saveImage: "Save annotations"

imageCaptionModal:
  title: "Edit caption"
  caption: "Caption"
  filename: "File name"
  filetype: "File type"
  cancelCTA: "Cancel"
  updateCTA: "Update"

install:
  altTitle: Shape
  altDescription: Refreshingly simple construction management.
  installTitle: Install Shape
  installDescription: Get the best Shape experience by installing the app on your device.
  mobileDescription: Get the best experience on your device.
  mobileButton: Install

installShape:
  title: Install Shape
  description: Shape works better on the app. Stay up-to-date with your site and interact with it.
  primaryCTA: Install
  cancelCTA: Later
  iOS:
    title: Install Shape
    descriptionPrefix: Quickly access Shape and never miss an update. Tap the share icon
    descriptionPostfix: and then select "Add to Home Screen" to stay connected on the go.

issue:
  new:
    title: "New issue"
    options:
      complete:
        title: Complete
        description: Go step by step through all the details needed.
      quick:
        title: Quick
        description: Only needs a title. Best choice for people who are on site with a mobile device.
      smart:
        title: Smart issue
        description: Drop in a quick blurb and any photos. We'll fill out the rest.
        aiPoweredBeta: AI-powered Beta
    controls:
      saveCTA: "{ SCREEN, select, large {Save draft} other {Save} }"
      backCTA: "Back"
      nextCTA: "Next"
    savedDraftOverlay:
      title: "Issue draft saved!"
      description: "Your issue is now saved as a draft. You can edit, add more details or delete it. It won’t be visible to anyone until you submit it."
      primaryCTA: "OK!"
      deleteCTA: Delete draft
    deleteDraftOverlay:
      title: "Delete draft"
      description: "Are you sure you want to delete this draft? This action cannot be undone."
      cancelCTA: Cancel
      deleteCTA: Delete
    dueDate:
      description: When is this issue due?
      labels:
        dueDate: "Due date"
        dueTime: "Due time"
    titleAndDescription:
      description: What's the issue?
      labels:
        title: "Title"
        titlePlaceholder: "Briefly describe the issue"
        description: "Description"
        descriptionPlaceholder: "Add more details"
    category:
      description: What's the issue type?
    assign:
      description: Choose a responsible person
    location:
      description: What's the location?
    discipline:
      description: What type of work is affected?
      level0: All disciplines
      level0ShortCode: ALL
  quickCapture:
    title: "New quick issue"
    hint: "If you need to update or add more details,<br/>visit the Issues page."
    fields:
      title:
        label: "Title"
        placeholder: "Short description"
        errors:
          required: "Title is required"
          length: "Title too long"
      description:
        label: "Description"
        placeholder: "Add more details if needed"
      media:
        label: "Upload an image"
        invalidSizeError: 'The file "{filename}" needs to be greater than {min} bytes and less than {max} MB'
      createAnother:
        label: "Create another"
    actions:
      cancel: Cancel
      create: Create
  smartIssue:
    title: Smart issue
    subtitle: This AI-Powered feature is in Beta. Please review the draft created.
    fields:
      content:
        label: Add a quick blurb and we'll find out the rest
        placeholder: Describe the issue, attach photos, and we'll prepare a pre-filled issue for you.
        errors:
          required: Please enter a description
      media:
        label: Media
        invalidSizeError: 'The file "{filename}" needs to be greater than {min} bytes and less than {max} MB'
    actions:
      cancel: Cancel
      generate: Generate
      generating: Generating
    toasts:
      failed: Something went wrong while creating your issue. Please try again.
  uploadSync:
    status:
      success: Your unpublished issue has been saved.
      offline: Your issue has been saved. We'll try to upload it later on Issues > Unpublished tab once you're online.

  draft:
    title:
      empty: No title entered
    createdAt:
      title: "Created"
      empty: N/A
    updatedAt:
      title: "Updated"
      empty: N/A

  drafts:
    actions:
      selectAll: Select all
      deselectAll: "Deselect all"
    deleteModal:
      title: "{count, plural, one {Delete draft} other {Delete drafts}}"
      subtitle: "{count, plural, one {This issue will be permanently removed.} other {These issues will be permanently removed.}}"
      cancelCTA: Cancel
      deleteCTA: Delete

  detail:
    export:
      exportForm:
        instructions: "Select the elements to include in your report."
        issue: Issue
        project: Project
        media:
          caption: Added {addedAt}
        title: Export issue
      actions:
        cancel: Back to the issue
        export: Export
      events:
        delete_image: "{ownerName} deleted an image"
        update_image: "{ownerName} updated an image"
        upload_image: "{ownerName} uploaded an image"
        delete_document: "{ownerName} deleted a document"
        upload_document: "{ownerName} uploaded a document"

    title:
      empty: No title entered
      edit: Edit issue title
    banners:
      archived: "This issue is archived."
      draft: "This issue is a draft and will not be visible until published."
    details:
      title: "Details"
    description:
      collapsableText:
        title: Description
        noContent: Not entered
      editModal:
        title: Edit description
        inputLabel: Description
    media:
      title: "Media"
      tabs:
        images: "Images"
        documents: "Files"
        errors:
          file_too_big: 'The file "{filename}" exceeds the maximum allowed size of {max} MB'
          file_size_zero: 'The file "{filename}" needs to be greater than {min} bytes'
      deleteItemConfirmationModal:
        title: Delete this item?
        subtitle: This action can't be undone.
        deleteCTA: Delete
        cancelCTA: Cancel
    types:
      closeOut:
        label: "Punch list"
        shortLabel: "Punch list"
      somethingNeeded:
        label: "Something needed"
        shortLabel: "Something needed"
      safety:
        label: "Safety"
        shortLabel: "Safety"
      progress:
        label: "Progress"
        shortLabel: "Progress"
      incompleteWork:
        label: "Outstanding/incomplete work"
        shortLabel: "Inc. work"
      damage:
        label: "Damage"
        shortLabel: "Damage"
      minorDeviation:
        label: "Minor deviation"
        shortLabel: "Minor dev"
      defect:
        label: "Defect"
        shortLabel: "Defect"
      access:
        label: "Access"
        shortLabel: "Access"
      material:
        label: "Material"
        shortLabel: "Material"
      equipment:
        label: "Equipment"
        shortLabel: "Equipment"
      design:
        label: "Design"
        shortLabel: "Design"
      qualityDocument:
        label: "Quality document"
        shortLabel: "QA doc"
      safeWorkingDocument:
        label: "Safe work document"
        shortLabel: "Safe work doc"
      permitOrConsent:
        label: "Permit or consent"
        shortLabel: "Permit"
      hazard:
        label: "Hazard"
        shortLabel: "Hazard"
      positiveObservation:
        label: "Positive observation"
        shortLabel: "Pos obv"
    archive:
      title: Archive issue
      subtitle: Please select the reason to archive.
      optionTitle: Select archive reason
      options:
        withdrawn: Withdrawn
        superseded: Superseded
        duplicated: Duplicated
        other: Other
      dismiss: *actionCancel
      confirm: *actionArchive
    restore:
      confirmation:
        title: Restore issue
        subtitle: Are you sure you want to restore this issue?
        dismiss: *actionCancel
        confirm: *actionRestore
    header:
      issuesStaleness:
        idle: This issue is idle
        stale: This issue is stale
      overdue: Overdue
      critical: Critical
      updates: "{ COUNT, plural, one {# update} other {# updates} }"

    archived: Archived
    events:
      ACCEPT_ASSIGNMENT: "accepted the assignment"
      ADD_APPROVER: "{ USERS, plural, one {added an approver} other {added the following approvers} }"
      ADD_TEAM: "{ TEAMS, plural, one {added a organisation to the issue} other {added the following organisations} }"
      APPROVE: "approved the issue"
      ARCHIVED: "archived the issue"
      ASSIGN: "assigned the issue to"
      CHANGE_STATUS: "changed the status"
      COMMENT_ON: "added a comment"
      COMPLETE: "marked the issue as complete"
      CREATE: "published the issue"
      DELETE_DOCUMENT: "deleted the file"
      DELETE_IMAGE: "deleted an image"
      PRIVATE_COMMENT_ON: "added a private comment"
      REJECT_ASSIGNMENT: "rejected the assignment"
      REJECT_RESOLUTION: "rejected the resolution request"
      REMOVE_APPROVER: "{ USERS, plural, one {removed an approver} other {removed the following approvers} }"
      REMOVE_TEAM: "{ TEAMS, plural, one {removed an organisation} other {removed the following organisations} }"
      REOPEN: "reopened the issue"
      RESOLVE: "resolved the issue"
      RESTORE: "restored the issue"
      STOP_PROGRESS: "stopped progress"
      UPDATE: "updated"
      UPDATE_OBSERVER: "changed the observer to"
      UPLOAD_DOCUMENT: "uploaded the file"
      UPLOAD_IMAGE: "uploaded an image"
    impact:
      formTitles:
        addImpact: "Add impact"
        editImpact: "Edit impact"
        workAffected: "Edit work affected"
        dueDate: "Edit due date"
        delayStart: "Edit delay start date"
        delayFinish: "Edit delay finish date"
        plannedClosureDate: "Edit planned closure date"
      workAffected:
        label: "Work affected"
      impact:
        notEntered:
          label: "Impact not entered"
          groupingOptionLabel: "Not entered"
          description: "The impact has not been entered."
        noEffect:
          label: "No effect"
          groupingOptionLabel: "No effect"
          description: "This issue has no effect on other works."
        potentialDelay:
          label: "Potential delay"
          groupingOptionLabel: "Potential delay"
          description: "The issue has the potential to cause delay to other works, but there is no impact yet."
        liveDelay:
          label: "Live delay"
          groupingOptionLabel: "Live delay"
          description: "The issue is preventing or slowing down progress on other works."
        completedDelay:
          label: "Completed delay"
          groupingOptionLabel: "Completed delay"
          description: "The issue has already delayed other works, but the delay is completed."
      dates:
        afterDelayStart: "The date must be in the past and after the delay start."
        beforeDelayFinish: "The date must be in the past and before the delay finish."
        delayFinish: "Delay finish"
        delayStart: "Delay start"
        dueDate: "Due date"
        shouldBeFuture: "The date should be in the future."
        shouldBePast: "The date should be in the past."
        noDate: "Not yet entered"
        plannedClosureDate: "Planned closure date"
    actions:
      reject: Issue rejected
      approve:
        closed: Thanks. Issue closed!
        final: Thanks. It's with {name} for final approval
        inProgress: Thanks. It's with {name} for approval
      start: Issue started
      stop: Issue not started
      complete: Issue completed. It's with {name} to approve
      reopen: Issue reopened
    assignments:
      accept: Issue accepted
      reject: Issue rejected
      assignedTo: Issue assigned to {name}
    state:
      draft: Draft
      assignment_rejected: Unassigned
      assigned: Not started
      assignment_requested: Pending responsible
      in_progress: Started
      completed: Pending approval
      resolved: Resolved
      default: Other
    people:
      title: People
      observer:
        title: Observer
      responsible:
        title: Responsible person
        overlay:
          title: Edit responsible person
          subTitle: Select one responsible person and click assign.
          confirmCTA: Assign
        resolvedError:
          title: Ooops!
          subtitle: Resolved issues cannot be reassigned. Please reopen the issue to change the responsible person.
          acknowledgeCTA: Okay

    approvers:
      title: "Approvers"
      edit:
        title: "Edit approvers"
        subtitle: "Add, reorder or remove approvers."
        addApproverCTA: "Add approver"
        cancelCTA: "Cancel"
        noApprover:
          title: "No approver selected"
          subtitle: "There must be at least one approver."
        saveCTA: *actionSave
      add:
        title: "Add approver"
        subtitle: "Select one person to add as an approver."
        backCTA: "Go back"
        addApproverCTA: "Add approver"
      resolvedError:
        title: Ooops!
        subtitle: You cannot change the approvers of an issue while it is resolved. Please reopen it to change approvers.
        acknowledgeCTA: Okay
    stateActions:
      assignmentRequested:
        meAssigner: "You have"
        otherAssigner: "has"
        action: "assigned this issue to"
        meResponsible: "you."
        workflow:
          reject:
            title: "Reject assignment"
            subTitle: "Are you sure you want to reject this assignment?"
            buttonCTA: "Reject"
            confirmCTA: "Reject"
            cancelCTA: "Cancel"
          approve:
            title: "Accept assignment"
            subTitle: "Are you sure you want to accept this assignment?"
            buttonCTA: "Accept"
            confirmCTA: "Accept"
            cancelCTA: "Cancel"
            plannedClosureDate:
              title: "Add a planned closure date"
              skipCTA: "Skip"
              submitCTA: "Submit"
        resolveAction: "has not yet accepted this issue."
      assigned:
        introAction: "The issue is with"
        meResponsible: "you"
        endAction: "to rectify."
        approverPosition: "You are approver {position} of {length}."
        displayedState: "Not started"
        workflow:
          approveCTA: "Approve"
          resolveCTA: "Resolve"
          complete:
            title: "Do you want to attach evidence to the completed issue?"
            subTitle: "This helps the issue to be accepted more quickly by the approver."
            confirmCTA: "Attach evidence"
            skipCTA: "Skip"
            confirmTitle: "Attach evidence"
            addMoreCTA: "Add more"
            finishCTA: "Finish"
          completeCTA: "Complete"
          inProgressCTA: "Mark as started"
      draft:
        publishCTA: Publish
        publishConfirmation:
          title: Add impact before publishing?
          subtitle: It seems like this issue has no impact yet. Defining the impact will determine if this issue has no effect, has potential delay, has live delay, or has a completed delay.
          publishWithoutImpactCTA: Publish without impact
          addImpactCTA: Add impact
        publishSuccess:
          issue: "Issue published."
          space: "Use this space to collaborate and track its history."
          assignedToMe: "You can Resolve this issue by clicking the Resolve button at anytime."
          assignedToOthers: "{assignee} will now receive an email informing them of their assignment."
      inProgress:
        action: "The issue has started."
        approverPosition: "You are approver {position} of {length}."
        displayedState: "Started"
        workflow:
          approveCTA: "Approve"
          outstandingCTA: "Mark as not started"
          completeCTA: "Complete"
      assignmentRejected:
        meAssignee: "You have"
        otherAssignee: "has"
        action: "rejected the issue."
        workflow:
          reassignCTA: "Reassign"
          assignCTA: "Assign"
      completed:
        singleApprover: "marked the issue as complete. It’s with you for final approval."
        firstApproverOfMultiple: "marked the issue as complete. It’s with you for approval."
        nextApproverOfMultiple: "approved the issue. It’s with you for approval."
        nextApproverAndFinal: "approved the issue. It’s with you for final approval."
        finalApprover: "The issue is complete. You are the final approver."
        hasBeenApprovedAfterCurrentUserHasNot: (approver {index}/{length}) has approved the issue. You are approver {position}/{length}
        multipleApproversNoApprovals: "marked the issue as complete. You are approver {position} of {length}."
        default: "You are approver {position} of {length}."
        workflow:
          rejectCTA: "Reject"
          approveCTA: "Approve"
          resolveCTA: "Resolve"
      resolved:
        resolvedBy: "The issue is resolved."
        reopen: "Reopen"
    stateWorkflows:
      rejectResolve:
        inProgressCTA: "Started"
        outstandingCTA: "Not started"
        subTitle: "What status do you want to revert to?"
        title: "Reject completion"
        form:
          title: "Add reason"
          field: "Why is the issue not resolved?"
          cancelCTA: "Cancel"
          rejectCTA: "Reject"
      assign:
        title: "Assign responsible person"
        subTitle: "Select one responsible person and click assign."
        cancelCTA: "Cancel"
        skipCTA: "Skip"
        submitCTA: "Assign"
      approveResolve:
        dialog:
          title: "Approve completion"
          subTitle: "Did this issue delay your work?"
          no: "No"
          yes: "Yes"
        form:
          title: "{ STEP, select, workAffected {Enter the work affected} startFinish {When did the delay start and finish?} finish {When did the delay finish} other {}}"
          workAffected:
            field: "Work affected"
            error: "Invalid work affected"
          finishDelay:
            startDelayField: "Delay start:"
            finishDelayField: "Delay finish:"
            error: "The date must be in the past and after the delay start."
            warning: "The delay start date should be in the past."
          next: "Next"
          previous: "Previous"
          submit: *actionSave
      reopen:
        title: Reopen issue
        subTitle: Are you sure you want to reopen this issue? The issue will be marked as "Not started" again.
        confirmCTA: Reopen
        cancelCTA: Cancel
    visibility:
      title: "Visibility"
      details: "Details"
      all: "All"
      projectWideTitle: "Public"
      projectWideSubtitle: "Anyone on the project can see this issue"
      privateTitle: "Private"
      privateSubtitle: "Only your organisation can see this issue"
      specificTeams:
        title: "Specific organisations"
        subtitle: "Only the following organisations can see this issue:"
        add: "Add organisation"
        overlay:
          title: "Add organisation"
          subtitle: "Select one organisation to add to specific organisations."
          members: "{ COUNT, plural, one {{COUNT} Member} other {{COUNT} People} }"
      edit:
        title: "Edit visibility"
        cancelCTA: "Cancel"
        saveCTA: *actionSave
      view:
        title: "View visibility"
        privateComms:
          subtitle: Organisation comms is only visible to your organisation
          yourTeam: Your organisation
          teamMembers: People
        sharedComms:
          subtitle: "Shared comms is visible to anyone who can view this issue"
          issueVisibility:
            title: "Issue visibility"
            members: "{ COUNT, plural, one {{COUNT} member} other {{COUNT} people} }"
        cancelCTA: "Cancel"
        saveCTA: *actionSave

    location:
      edit: "Edit location"
      observedAt: "Observed at"
      noDate: "Not yet entered"
    status:
      title: "Status"
      update: "Update status"
      completed: "Completed"
      selectStatusCTA: "Select status"
      saveCTA: Save
      nextCTA: Next
      cancelCTA: Cancel
    statusStatement:
      title: "Latest status statement"
      placeholder: Make a statement to describe this issue's status.
      shortTitle: "Status:"
      addCTA: "Add"
      viewCTA: "View"
      viewOrUpdateCTA: "View or update"
      date:
        dateLabel: "Applies since"
        weeks: "weeks"
      drawer:
        title: "Status statement"
        subtitle: "An update that adds clarity to the issue's progress."
      form:
        statement:
          label: "Give a new status statement"
          placeholder: 'E.g. "Awaiting response on the technical query"'
          successToast: "Status statement added"
        date:
          label: "Applies since"
        submitCTA: "Submit statement"
      history:
        title: "Previous status statements"
        placeholder:
          title: "No status statements yet"
          subtitle: "The history of status statements will be shown here."
    dates:
      closedAt: "Closed at"
      observedAt: "Observed at"
      noDate: "Not yet entered"
    datesForm:
      date: Date
      time: Time
      closedAt: "Edit closed at"
      closedAtNote: "Manually enter the date and time the issue was closed"
      observedAt: "Edit observed at"
      observedAtNote: "Manually enter the date and time the issue was first observed"
      shouldBeBeforeClosedAt: "The date must be before the closed at."
      shouldBeAfterObservedAt: "The date must be after the observed at."
    category:
      edit: Edit type
      empty: No category entered
    discipline:
      edit: "Edit discipline"
      empty: No discipline entered
    createdAt:
      title: "Created at"
    updatedAt:
      title: "Updated at"
    topActions:
      export: Export
      watch: "{ ISWATCHING, select, true {Unwatch} other {Watch} }"
      moreMenu:
        print: Print
        critical: "{ CURRENT, select, true {Unmark as critical} other {Mark as critical} }"
        archive: *actionArchive
        restore: *actionRestore
      share:
        title: "Share issue"
        content: "Make sure whoever you shared this issue with are able to access it. You may need speak to your project's admin to grant them access."
        shareCTA: "Share"
        shareLinkTitle: "Shape item: {title}"
        shareLinkContent: "Click the link to see this Shape item. Title:  {title}"
      savedDraft: "Saved draft"
    activity:
      tabs:
        details: "Details"
        teamComms: "Private comms"
        sharedComms: "Updates & comments"
        gallery: "Gallery"
      input:
        placeholder: "Add a comment"
        hint:
          open: "Anyone seeing this issue can see your comment."
          team: "Only people in your organisation can see your message."
      public:
        empty:
          title: No conversations yet
          message: This space is for you to exchange thoughts and ideas. All messages are public.
      team:
        banner: "Only visible to your organisation."
        empty:
          title: No conversations yet
          message: This space is for you to exchange thoughts and ideas. All messages are accessible only by members of the organisation.
      delete:
        confirmation:
          title: "Are you sure?"
          subTitle: "You can't undo this action, do you want to delete this message?"
          confirmCTA: "Delete"
          dismissCTA: "Cancel"
        error:
          title: "Ooops!"
          subTitle: "Sorry, we can't update your comment now, please try again."
          acknowledgeCTA: "Okay"
    customFields:
      title: *customFieldsTitle
      projectWide: "Project wide"
      team: "Organisation"
      noData: "Not yet entered"
      editAll: Edit all
      editForm:
        title: "Edit custom { count, plural, one {field} other {fields} }"
        fieldValue:
          characters: characters
          errors:
            length: Field value too long
  gallery:
    emptyPlaceholder:
      subtitle: Issue documents will appear here as soon as they are uploaded.
  list:
    title: Issues
    export:
      exportCTA: Export
      title: Export issues
      modal:
        infoBanner: Exporting issues from your current view.
        pdfExportLimit: Only the latest 100 created issues will be included in this export.
        label: What format do you need?
        confirmCTA: Export
        title: Export issues
        contentLabel: "Choose a format:"
        inputTitle: "Set a report title (optional):"
        inputPlaceholder: Issues report
        cancelCTA: Cancel
        exportCTA: Export
      confirmModal:
        title: Issues exported
        subTitle: You will get an email when the export is ready.
        dismissCTA: Dismiss
    options:
      otherActions: Other actions
      collapseAll: Collapse all
      sortAscending: Sort ascending
      sortDescending: Sort descending
      saveViewCTA: Save
      saveViewSuccess: Your view options have been saved.
    filters:
      label: Filters
      options: Options
      viewOptions: View options
      groupBy: Group by
      sortBy: Sort by
      clearCTA: Clear all
      cancelCTA: Cancel
      applyCTA: Apply
      group:
        details: Details
        people: People
        dates: Dates
        visibility: Visibility
      publishedDate:
        label: Date created
        filterBy: Filter by date created
        applyLabel: Apply
      dueDate:
        label: Due date
        filterBy: Filter by due date
        applyLabel: Apply
      updatedDate:
        label: Last updated at
        filterBy: Filter by last updated at
        applyLabel: Apply
      plannedClosureDate:
        label: Planned closure date
        filterBy: Filter by planned closure date
        applyLabel: Apply
      closedDate:
        label: Closed at
        filterBy: Filter by closed at
        applyLabel: Apply
      originator:
        label: Originator
        currentUserText: (Me)
        filterBy: Filter by originator
        searchPlaceholder: Search
      responsible:
        label: Responsible person
        currentUserText: (Me)
        filterBy: Filter by responsible person
        searchPlaceholder: Search
      impact:
        label: Impact
        filterBy: Filter by impact
        noEffect:
          label: No effect
          description: Issue has no effect on other works.
        potentialDelay:
          label: Potential delay
          description: Issue can cause delay to other works in the future.
        liveDelay:
          label: Live delay
          description: Issue is currently preventing or slowing down progress on other works.
        completedDelay:
          label: Completed delay
          description: Issue has already delayed other works, but has been resolved.
        notEntered:
          label: Not entered
          description: Issue does not have an impact entered.
      filterState:
        label: Status
        filterBy: Filter by status
        unassigned: Unassigned
        pendingResponsible: Pending responsible
        notStarted: Not started
        started: Started
        pendingApproval: Pending approval
        resolved: Resolved
      category:
        label: Issue type
        title: Filter by issue type
        searchPlaceholder: Search for issue type
        noCategory: No issue type
      discipline:
        label: Discipline
      location:
        label: Location
        title: Locations
        searchPlaceholder: Search for a location
        noLocation: No location
      responsibleTeam:
        label: Responsible organisation
        filterBy: Filter by responsible organisation
        searchPlaceholder: Search
      visibility:
        all: All
        label: Visibility
        filterBy: Filter by visibility
        private:
          label: Private
          description: Show issues visible only to your organisation
        public:
          label: Public
          description: Show issues visible to anyone in the project
        specificOrganisations:
          label: Specific organisations
          description: Show issues visible only to the selected organisations
          members: "{COUNT, plural, =0 {No members} one {# member} other {# members} }"
        specificOrganisationModal:
          title: Specific organisations
          subTitle: You are filtering issues visible to the following organisations.
          noOrganisationSelected: No organisations selected yet
          addOrganisationCTA: Add organisation
          applyCTA: Apply
        addOrganisationModal:
          title: Add organisation
          subTitle: Select one organisation to add to specific organisations.
          description: "You are filtering issues visible to the following organisations:"
          noOrganisationAvailable: No available organisations
          goBackCTA: Go back
          addCTA: Add
    view:
      actions:
        tooltip: Manage
      new:
        title: New view
        description: Any filtering, sorting, and grouping you do within the view will automatically be saved.
        form:
          viewName: View name
          viewNamePlaceholder: Give this new view a name
          requiredMessage: Required field
          clearCTA: Clear all
        createCTA: Create view
        cancelCTA: Cancel
      edit:
        title: Rename
        form:
          title: Rename view
          viewName: View name
          viewNamePlaceholder: Give this view a name
          requiredMessage: Required field
        createCTA: Save
        cancelCTA: Cancel
        successMessage: “{oldName}” has been renamed to “{newName}”.
      delete:
        title: Delete
        confirmation:
          title: Delete view
          subtitle: Are you sure you want to delete this view? This action can’t be undone.
          dismiss: *actionCancel
          confirm: *actionDelete
          successMessage: Created view has been successfully deleted.
    grouping:
      maximumSelectedWarning: Maximum number of nested groups reached.
    tabs:
      allIssues: "All issues"
      archived: "Archived"
      drafts: "Unpublished"
      myIssues: "My issues"
    emptyPlaceholder:
      default:
        title: You don't have any issues
        subtitle: Create an issue to track its updates, add comments, and share with other stakeholders.
      all:
        title: This project has no issues yet
        subtitle: Issues will appear here once someone creates an issue.
      drafts:
        title: You don't have any unpublished issues
        subtitle: ""
      archived:
        title: You don't have any archived issues
        subtitle: ""
      hasFilters:
        title: No filter results
        subtitle: Please check your view filters.
  options:
    actions:
      collapseAll: Collapse all
      sortAscending: Sort ascending
      sortDescending: Sort descending
      reset: Reset
      groupBy: Group
      sortBy: Sort
    # @deprecated Labels for `group` are deprecated. Use groupBy instead
    group:
      assignedTeamMember: "Responsible person"
      assignedTeam: "Responsible organisation"
      customField: "Custom fields"
      discipline: "Discipline"
      impact: "Impact"
      location: "Location"
      observer: "Observer"
      observerTeam: "Observer's organisation"
      state: "Status"
    groupBy:
      description: Group by
      responsiblePerson: Responsible person
      responsibleTeam: Responsible organisation
      discipline: Discipline
      impact: Impact
      location: Location
      originator: Originator
      originatorTeam: Originator's organisation
      state: Status
      customField: Custom fields
      none: None
      custom:
        title: Custom
        description: Create your own groupings by combining the options above.
        modal:
          title: Create custom groups
          description: Create your own groups by combining subgroup options.
          subtitle: Applied subgroups
          addSubgroup: Add subgroup
          createCTA: Create
          cancelCTA: Cancel
          removeGroupCTA: Remove
          noGroups: No groups applied
    # @deprecated Labels for `sort` are deprecated. Use sortBy instead
    sort:
      created: "Created date"
      delay_finish: "Delay finish"
      delay_start: "Delay start"
      due_date: "Due date"
      planned_date: "Planned closure date"
      reference: "Reference"
      title: "Issue title"
      updated: "Updated date"
    sortBy:
      description: Sort by
      created: Created on
      delay_finish: End of delay
      delay_start: Start of delay
      due_date: Due on
      planned_date: Planned closure
      reference: Reference number
      title: Issue title
      updated: Last updated on
      status: Status
      impact: Impact
  events:
    state:
      sending: Sending...
    accept_assignment: "{ownerName} accepted the assignment"
    add_approver: "{approversCount, plural, one {{ownerName} added {approvers, list} as an approver} other {{ownerName} added {approvers, list} as approvers}}"
    add_team: "{teamsCount, plural, one {{ownerName} added the organisation {teams, list}} other {{ownerName} added the organisations {teams, list}}}"
    add_status_statement: "{ownerName} added a new status statement: {statusStatement}"
    delete_status_statement: "{ownerName} deleted the status statement: {statusStatement}"
    approve: "{ownerName} approved the issue"
    archive: "{ownerName} archived the issue"
    assign: "{ownerName} assigned the issue to {recipientName}"
    change_status:
      type: Status
      message: "{ownerName} changed the {status} from {from} to {to}"
    comment_on:
      delete: Delete comment
    create: "{ownerName} created the issue"
    reject_assignment: "{ownerName} rejected the assignment"
    reject_resolution: "{ownerName} rejected the resolution request"
    remove_approver: "{approversCount, plural, one {{ownerName} removed {approvers, list} as an approver} other {{ownerName} removed {approvers, list} as approvers}}"
    remove_team: "{teamsCount, plural, one {{ownerName} removed the organisation {teams, list}} other {{ownerName} removed the organisations {teams, list}}}"
    reopen: "{ownerName} reopened the issue"
    restore: "{ownerName} restored the issue"
    update:
      field: "{updateField} to {value}"
      message: "{parametersCount, plural, one {{ownerName} updated {parametersArray, list}} other {{ownerName} updated {parametersArray, list}}}"
      change_field: "{updateField} from {from} to {to}"
      clear_field: "{updateField} cleared from {from}"
      update_field: "{updateField} to {value}"
    update_impact:
      field: "{updateField} to {value}"
      message: "{ownerName} changed the impact: {parametersArray, list}"
      field_impact: Impact
      field_due_date: Due date
      field_delay_start: Delay start date
      field_delay_finish: Delay finish date
      field_work_affected: Work affected
      change_field: "{updateField} from {from} to {to}"
      clear_field: "{updateField} cleared from {from}"
      update_field: "{updateField} to {value}"
    update_observer:
      type: Observer
      message: "{ownerName} changed the {observer} to {updatedObserverName}"
    upload_document: "{ownerName} uploaded {documentName}"
    new: New

loadMoreButton:
  loading: Loading
  loadMore: Load more {buttonType}

locale:
  options:
    en:
      label: English
      subLabel: English
    es-419:
      label: Español (Latinoamérica)
      subLabel: Spanish (Latin America)

myProjects:
  title: My Projects
  currentProjects: Current projects
  archivedProjects: Archived
  emptyState:
    projectsTitle: You don't have any projects yet.
    archivedProjectsTitle: You don't have any archived projects.
    subTitle: You can get in touch with your project manager and request access. Looking to get started by yourself? Click above to start crushing issues.
  newProject: New project

myAccount:
  title: My account
  details: Account details
  edit: Edit
  privacy: Privacy
  cookiePreferences: Cookie preferences
  cookieDeclaration: Cookie declaration

navigation:
  back: Back
  backTo: Back to {route}
  dashboard: Dashboard
  home: Home
  issues: Issues
  timeline: Timeline
  activities: Activities
  notifications: Notifications
  search: Search issues
  shiftReports: Shift reports
  gallery: Gallery
  channels: Channels web
  weeklyPlanner: Weekly planner
  changeTracker: Change tracker
  dataBook: Data book

projectForm:
  newTitle: New project
  editTitle: Edit project
  fields:
    projectName: Project name
    shortName:
      label: Short name
      description: This is a quick way to identify your project. It will show as a reference on all issues in the project e.g issues in a project with the short name RES may have the reference RES-FIN-0001.
    projectAvatar:
      label: Project avatar
      ariaLabel: Upload a project avatar
      uploadDescription: You can upload a file PNG, JPG or JPEG up to {max}MB. If you leave it blank, an avatar with chosen short name will be used.
      uploadErrorMessage: The file selected has an unsupported extension or must be greater than {min} bytes and smaller than {max} MB
    timezone: Timezone
    uploadCTA: Upload
  actions:
    cancel: *actionCancel
    save: *actionSave
    saveDemo: Save demo project
  showcaseProjectLoading: Creating demo project. It usually takes a couple of minutes. Please wait...
  projectCreatedFeedback: Project created.
  projectEditedFeedback: Project was successfully edited.

projectGallery:
  metadata:
    caption:
      add: Add caption
    location:
      add: Add location
      cancel: Cancel
      edit: Edit location
      save: Save
  upload:
    new: Upload
    alert: Details apply to all items
  uploadPreview:
    picker:
      trigger: Add documents
    thumbnail:
      document: Document
      removeDocument: Remove document
    save: Save
  emptyGalleryPlaceholder:
    title: Gallery is empty
    subtitle: Project documents will appear here as soon as they are uploaded.
  noResultsPlaceholder:
    title: No matching results
    subtitle: Try to adjust your filters or clear all to show results.
    clearCTA: Clear all filters
  filters:
    label: Filters
    document:
      searchPlaceholder: Search by caption
    author:
      label: Author
      placeholder: Search for a person
    format:
      label: Format
      all: All
      images: Images
      files: Files
    locations:
      label: Location
      title: Locations
      placeholder: Search for a location
      options:
        noLocation: No location
    clearAll: Clear all
  sort:
    label: Sort
    newest: Newest
    oldest: Oldest
  add: Add
  documentViewer:
    infoPanel:
      tabTitle: Details
      title: Upload information
      author: Author
      dateUploaded: Date uploaded
      source: Source
      uploadLocation: Location
      addLocation: Add location
      caption: Caption
      addCaption: Add caption
      sourceAndReferences:
        title: Source and references
        subscriptionPlan:
          title: This is a pro feature
          body: Unlock this and other features with a pro plan.
          link: Contact us
          learnMore: to learn more
        noSource: Gallery
        noReferences: No references yet
        subtitle:
          source: Source
          references: References
    propertiesTab:
      title: Properties
      type: Type
      size: Size
      dimensions: Dimensions
      pixelCount: Pixel count
      iso: ISO
      dateTaken: Date taken
      timeTaken: Time taken
      deviceModel: Device model
      geolocation: Geolocation
      noData: No data
  page:
    title: Gallery
  editDocumentModal:
    title: Edit document details
    saveCTA: Save
    cancelCTA: Cancel
    fields:
      caption: Caption
      location: Location
  document:
    error: Error on getting document

notAuthorised:
  title: Not authorised
  subtitle:
    userFromProject: You need to request access from admins to view this content.
    userOutsideProject: You are not authorised to view this page because you're not involved in this project. In order to view it, you need to request access from an admin.
  requestAccessCTA: Request access
  goToMyProjects: Go to My Projects
  goToProject: Go to {projectName}
  requestAccessModal:
    title: Request access
    fields:
      team:
        label: Organisation
        placeholder: Your organisation
        description: Which organisation do you work for? This will help route your request to the right person.
        errorMessage: Required field
      email:
        label: Send to
        placeholder: <EMAIL>
        description: If you know an admin, type in their email to get access quicker.
        errorMessage: Must be a valid email
      message:
        label: Message
        description: Add a message to let the admin know why you want access.
    cancelCTA: Cancel
    requestCTA: Send request
    successToast: Request sent.

notFound:
  title: Page not found
  subtitle: The page you requested could not be found.
  goToMyProjects: Go to My Projects
  goToProject: Go to {projectName}

notifications:
  page:
    title: "Notifications"
    markReadCTA: Mark all as read
    goBack: Go back
  permission:
    banner:
      title: Stay up to date.
      link: Enable push notifications
    global:
      label: Enable notifications?
      laterButton: Later
      enableButton: Enable now
    modal:
      completeTitle: Thanks!
      completeDescription: We'll keep you up-to-date!
      title: Stay up to date
      description: Getting notified as soon as something needs your attention is the best way to get issues closed faster.
      actionText: Update me
      testPrefix: permission
  popover:
    loadMore: See all notifications
  emptyState:
    title: No notifications yet
notification:
  error:
    title: "Ooops!"
    subTitle: "Sorry, there was an error doing this action, please try again. {message}"
    acknowledgeCTA: "Okay"

offline:
  title: You're offline
  subTitle: Looks like you're not connected to a network
  status: Network connectivity limited or unavailable.

otpConfirmationForm:
  inputPlaceholder: Enter {numberOfDigits}-digits code
  submit: Verify
  errors:
    default: The code is invalid
    lockedForConfirmation: Too many verification attempts. Your account has been locked.

print:
  printForm:
    instructions: "Select the elements you want to include in your report."
    issue: Issue
    project: Project
    media:
      caption: Added {addedAt}
    title: Print issue

  header:
    title: Issue report
    exportedOn: Exported on {currentDate} {currentTime}

  footer:
    credits: This document was generated by Shape
    appUrl: app.shape.construction

  actions:
    cancel: Cancel
    print: Print

profile:
  memberSince: "Member since {SIGNUP_DATE}"
  settings:
    avatar:
      change: Change
      uploadErrorMessage: The file selected has an unsupported extension or must be greater than {min} bytes and smaller than {max} MB
      modalTitle: Profile picture
      saveCTA: Update
      cancelCTA: Cancel
    unconfirmedEmail: "Currently waiting confirmation for <b>{EMAIL}</b>"
    updateEmail:
      title: "Update email address"
      subTitle: "Update your email and save. We’ll send you an email asking you to confirm the change."
      fields:
        email: Email
      alert:
        title: "Email update pending verification"
        description: "Check <b>{EMAIL}</b> to verify it’s you and complete the update."
        resend:
          CTA: "Resend email"
          action:
            success: "Unconfirmed email erased!"
            fail: "There was a problem erasing the unconfirmed email, please try again."
        cancel:
          CTA: "Cancel email change"
          action:
            success: "Unconfirmed email erased!"
            fail: "There was a problem erasing the unconfirmed email, please try again."
      action:
        success: "Verification email sent!"
        fail: "Your email could not be updated. Please try again."
    updateName:
      title: Update name
      subTitle: Update your first and last name and save.
      fields:
        firstName: First name
        lastName: Last name
      action:
        success: "Your name was updated!"
        fail: "Your name could not be updated. Please try again."
    updatePassword:
      title: "Update password"
      subTitle: "Enter your current and new password."
      fields:
        currentPassword: Current password
        newPassword: New password
        repeatNewPassword: Repeat new password
      action:
        success: "Your password was updated!"
        fail: "Your password could not be updated. Please try again."

projects:
  members: "{ count, plural, one {{count} member} other {{count} members} }"
  actions:
    editDetails: Edit details
    archive: *actionArchive
  tooltips:
    onlyAdminCanEdit: Only the project admin can edit the project's details
  archiveModal:
    title: Archive this project
    subTitle: Archiving this project will move it into your archived list where you can still access all of your data.
    actions:
      cancel: Cancel
      archive: *actionArchive
    feedback: Your project was archived. You can now find it in the “Archived” tab.

projectSetup:
  label: Project setup
  newProject: Create project
  createLocation: Create a location tree
  inviteTeamMember: Add a person
  inviteTeam: Add another organisation
  createCustomField: Create a custom field

quickNewButton:
  trigger:
    title: New
  issueTracker:
    heading: Issue tracker
    options:
      completeIssue:
        title: Complete issue
        description: Fill out all details about the issue.
      quickIssue:
        title: Quick issue
        description: Add at least a title and fill out details later.
      smartIssue:
        title: Smart issue
        description: Drop in a quick blurb and any photos. We'll fill out the rest.
        aiPoweredBeta: AI-powered Beta
  shiftManager:
    heading: Shift manager
    options:
      shiftReport:
        title: Shift report
  weeklyPlanner:
    heading: Weekly planner
    options:
      workPlan:
        title: Work plan
  gallery:
    heading: Gallery
    options:
      upload:
        title: Upload files & images
  activity:
    title: Activity

team:
  untitled: Untitled Organisation
  editTitle: Edit organisation's name
  upgradeCTA:
    label: Organisation page upgrade plan
    desktop: Upgrade plan
    mobile: Upgrade
  dropdownActions:
    remindToJoin: Remind to join
    editInvite: Edit invite
    cancel: Cancel
    edit: Edit
    remove: Remove
  remindAllToJoin:
    cta: Remind all to join
    feedback: Invites sent.
  remindToJoinFeedback: Invite sent.
  tabs:
    joined: Joined
    pending: Pending
    emptyState:
      joined:
        title: No one has joined this organisation yet.
        subTitle: Get started by adding users.
      pending:
        title: There are no pending invites.
        subTitle: Get started by adding users.
    subscription:
      upgrade: Upgrade plan
      update: Update plan
  table:
    header:
      user: User
      role: Role
      permissions: Permissions
  subscriptions:
    card:
      shapeGeneral: Shape (General)
      issueTracker: Issue Tracker
      shiftManager: Shift Manager
      dataBook: Data Book
      gallery: Gallery
      actions:
        current: Your current plan
        subscribe: Upgrade
        downgrade: Downgrade plan
        contactUs: Contact us
        manageBilling: Manage billing
        email:
          free:
            subject: Downgrade plan
            body: "Hello Shape Team. May I request the Free version downgrade for my organisation { teamName } [organisation id: { teamId }]."
          pro:
            subject: Upgrade plan
            body: "Hello Shape Team. May I request the Pro version upgrade for my organisation { teamName } [organisation id: { teamId }]."
            customDashboards:
              body: "Hello Shape Team. May I request the Pro version upgrade for my organisation { teamName } [organisation id: { teamId }]. I would like to create a custom dashboard for my team."
    checkout:
      error:
        title: Subscription failed
        subtitle: There was a subscription checkout error.
        actions:
          dismiss: Dismiss
    loading:
      title: Waiting for status update...
      description: Please don’t leave this page while waiting for the status update.
    error:
      title: Update failed
      description:
        We’ve encountered an unexpected error and failed to proceed with the upgrade. Please
        try again later or contact <NAME_EMAIL>.
      goToTeams: Go to organisations
    success:
      title: Update successful
      description: "{teamName} is now in a {planName} plan and has unlocked the following features"
      goToTeam: Go to organisation
    plans:
      free:
        title: Free
        subtitle: For individuals and small organisations to track and report what’s happening on site
        price: £0
        priceInfo: ""
        shapeGeneral:
          collaborators:
            name: Collaborators
            detail: Unlimited
          realtimeProjectTimeline:
            name: Real-time project timeline
            detail: ""
        issueTracker:
          fullIssueHistory:
            name: Full issue history
            detail: ""
          privateChannelPerIssue:
            name: Private channel per issue
            detail: ""
          offlineIssueCapture:
            name: Offline issue capture
            detail: ""
        shiftManager:
          createSiteDiary:
            name: Create site diary
            detail: Unlimited
          resourceAllocation:
            name: Resource allocation
            detail: ""
          addPhotoEvidenceToAnyLineItem:
            name: Add photo evidence to any line item
            detail: ""
          professionalPDFOutputs:
            name: Professional PDF outputs
            detail: ""
          saveEffortWithDataPrefill:
            name: Save effort with data pre-fill
            detail: ""
        dataBook:
          dataHealthHeatmap:
            name: Issues and shift reports heatmap
            detail: ""
        gallery:
          createSiteDiary:
            name: Traceable files & photos
            detail: ""
          automaticCentralisedStorage:
            name: Automatic, centralised storage
            detail: ""
          addCaptionsAndLocationsToAllFiles:
            name: Add captions and locations to all files
            detail: ""
          allPhotosAutoUploaded:
            name: All photos auto-uploaded
            detail: ""
      pro:
        title: Pro
        subtitle: For project organisations to better manage, report, and analyse construction projects
        price: Talk to us
        priceInfo: per person / month, billed monthly
        shapeGeneral:
          collaborators:
            name: Collaborators
            detail: Unlimited
          realtimeProjectTimeline:
            name: Real-time project timeline
            detail: ""
        issueTracker:
          everythingInFreePlus:
            name: "Everything in Free plus:"
            detail: ""
          excelDataExport:
            name: Excel data export
            detail: ""
          pdfIssueExport:
            name: PDF issues export
            detail: ""
          generateSingleIssueReport:
            name: Generate single issue report
            detail: ""
        shiftManager:
          everythingInFreePlus:
            name: "Everything in Free plus:"
            detail: ""
          saveTimeWithMultiUserMode:
            name: Save time with multi-user mode
            detail: ""
          excelDataExport:
            name: Excel data export
            detail: ""
          topDownDataQualityView:
            name: Top-down data quality view
            detail: ""
        dataBook:
          dataHealthHeatmap:
            name: Issues and shift reports heatmap
            detail: ""
          customDashboards:
            name: Custom dashboards
            detail: ""
          dataInsights:
            name: Data insights
            detail: ""

treeSelector:
  clearAll: Clear all

search:
  placeholder: Search
  issues:
    placeholder: Search issues
  people:
    currentUser: (me)
    placeholder: Search for a person
    allTeams: All Organisations
    addPerson: Add a person
    addThisPerson: Add this person
    emailSearch:
      title: Add "{EMAIL}"
      subtitle: Seems like this person is not yet on your project. You may set the permissions, role, and add this person by clicking "Add this person" button below.
    noResults:
      title: We can't find "{SEARCHTERM}" in the list
      subtitle: Please check the spelling or add by typing the person's email
    addPersonForm:
      title: Add a person
      subtitle: You may add a person to your project using their email. You may also set their permissions and role below.
      emailField: Email
      roleField: Permissions
      constructionRoleField: Role
      goBackCTA: Back to search
      submitCTA: Add and select
      billingInfo: You are adding 1 new user. An additional x 1 Shape user license ({pricePerLicense}) will be added to your billing.
    quotaBanner:
      badge: &quotaLimitsBadge You reached the maximum number of people in an organisation.
      teamPageCTA: Manage organisation
      upgradePlanCTA: Upgrade plan
  locations:
    title: Location
    placeholder: Search for a location
  disciplines:
    placeholder: Search for a discipline
  noResults:
    title: No matching search results
    subTitle: Please check spelling or try different keywords
  date:
    title: Date
    filter: Date filters
    apply: Apply
  planActivity:
    noResults:
      title: Can't find an activity?
      subtitle: It might not be in your work plan or there may be an existing progress log for it

appSidebar:
  home: Home
  projectsMenu: Projects menu
  selectProject: Select project
  dataBook: Data book
  admin: "Admin"
  upgradePlanButton:
    free:
      title: Upgrade plan
      description: Be more productive with Pro’s advanced features.
    trial:
      title: Upgrade plan
      description: "{trialDays} days left of your Pro trial. Click to upgrade."
  getShapeAndChannelsOnMobile:
    title: Get Shape & Channels on your phone
    popover:
      title: Get Shape & Channels on your phone
      shape:
        title: Shape
        step1a: Either tap
        step1b: for iOS or
        step1c: for Android
        step2: Select Add to Homescreen
      channels:
        title: Channels
        step1: Scan the QR code or search “Channels” on the App Store or Google Play Store
    modal:
      title: Get Shape and Channels on your phone
      shapeApp:
        title: Add shape to your home screen
        step1a: Scan the QR code or visit
        step1b: on your web browser
        step2: Login to your Shape account
        step3: Either tap for iOS or for Android
        step4: Select Add to Homescreen
      channelsApp:
        title: Download the Shape Channels standalone messaging app
        step1: Keep your work out of your personal messaging apps. Share files & photos instantly, and automatically sync them in your project gallery on Shape.
      print: Print

timeline:
  empty: There are no events.
  title: Timeline
  newActivity: New activity
  hideComments: Hide comments
  showAllComments: Show all comments
  filter:
    clear: Clear all
    applyCTA: Apply
    cancelCTA: Cancel
    date:
      menuTitle: Date
      menuSubTitle: Date filters
      from: From
      to: To
    event:
      menuTitle: Event types
      menuSubTitle: Type filters
      from: From
      to: To
      options:
        all: All
        none: None
        multiple: Multiple
    options:
      approve: "Approvals"
      changeStatus: "Status updates"
      commentOn: "Open chat"
      create: "New items"
      privateCommentOn: "Private chat"
      rejectResolution: "Approval rejections"
      reopen: "Issue reopenings"
      resolve: "Issue resolved (final approval)"
      update: "Issue edits"
      updateImpact: "Impact updates"
      uploadImage: "Image uploads"
  events:
    update_event:
      field_updated: Updated from {from} to {to}
      field_cleared: Cleared from {from}
      field_set: Updated to {to}
    card:
      title:
        eventTitle: "{title} {multiple, select, true {} other {by} } <user></user>"
        commentOn: "{number, plural, one {Comment} other {{number} comments}}"
        privateCommentOn: "{number, plural, one {Private comment} other {{number} private comments}}"
        uploadImage: "{number, plural, one {Image uploaded} other {{number} images uploaded}}"
        create: New issue
        approve: Issue approved
        reopen: Issue reopened
        changeStatus: Status update
        rejectResolution: Approval rejected
  share:
    title: "Share issue"
    content: "Make sure whoever you shared this issue with are able to access it. You may need speak to your project's admin to grant them access."
    shareCTA: "Share"
    shareLinkTitle: "Shape item: {title}"
    shareLinkContent: "Click the link to see this Shape item. Title:  {title}"

profileMenu:
  appVersion: Shape App version {version}
  signedInAs: Signed in as
  menu:
    myAccount: "My account"
    myProjects: "My projects"
    installApp: "Install Shape"
    sendFeedback: "Send feedback"
    logout: "Logout"
    language: Language

weeklyPlanner:
  workPlans:
    title: Weekly work plans
    newPlanCTA: New work plan
    tabs:
      myPlans: My plans
      all: All plans
      closed: Closed
      archived: Archived
    table:
      heading:
        name: Name
        period: Period
        author: Author
        lastUpdated: Last Updated
        status: Status
      emptyStates:
        myPlans:
          title: You don’t have any work plans
          description: Create a work plan to plan your activities for the week and track their progress as the week goes by.
          action: New work plan
        all:
          title: This project has no work plans yet
          description: Work plans will appear here once anyone creates a work plan.
          action: New work plan
        closed:
          title: There are no closed work plans yet
          description: Work plans will appear here once anyone closes out a plan that has ended.
        archived:
          title: There are no archived work plans yet
          description: Work plans will appear here once anyone archives a work plan.
          action: New work plan
      you: (You)
      actions:
        publishPlan: Publish plan
        publishLookback: Publish lookback
        edit: Edit
        duplicate: Duplicate
        archive: Archive
        restore: Restore
        tooltips:
          menu: Menu
          export: Export work plan
          noEditPermission: You do not have permission to edit this work plan
          noEditClosed: This work plan is uneditable because it is closed
          noDuplicatePermission: You do not have permission to duplicate this work plan
          noArchivePermission: You do not have permission to archive this work plan
          noRestorePermission: You do not have permission to restore this work plan
      archivePlan:
        successToast: '"{planName}" was archived.'
      restorePlan:
        successToast: '"{planName}" was restored. You can find it in My plans/All plans.'
    newPlan:
      title: New plan
      cancelCTA: Cancel
      createCTA: Create
    editPlan:
      title: Edit plan
      cancelCTA: Cancel
      saveCTA: Save
    duplicatePlan:
      title: Duplicate plan
      cancelCTA: Cancel
      duplicateCTA: Duplicate
    publishPlan:
      publish: Publish plan
      modal:
        title: Publish plan
        subTitle: The work plan will be made visible to others. You will not be able to edit the plan once finalised.
        cancelCTA: Cancel
        confirmCTA: Confirm
        successToast: The plan is now visible to others
    publishLookback:
      publish: Publish lookback
      modal:
        title: Publish lookback
        subTitle: Are you sure you want to finalise all details in the lookback? This will close this work plan permanently and download a PDF report.
        cancelCTA: Cancel
        confirmCTA: Confirm
        successToast: "{planTitle} lookback was published.<br/><br/>A PDF copy of the work plan will be downloaded."
    form:
      fields:
        planName:
          label: Plan name
          placeholder: eg. Week 21 Night Shift
          maxLengthLimit: "{inputLength} character(s) left."
        duration:
          label: Duration
          startDatePlaceholder: Start date
          endDatePlaceholder: End date
    planEditor:
      header:
        activityName: Activity name
        location: Location
        organisation: Organisation
        responsible: Responsible
        status: Status
        ready: Ready?
        requirements: Requirements
        planActivityStatus: Planned?
        progress: Progress now
        expectedProgressDate: Expected progress by <br/> {expectedEndDate}
        schedule: Schedule
        comment: Comment
        actions:
          editPlanDetails: Edit plan details
          duplicate: Duplicate
          archive: Archive
          restore: Restore
          export:
            label: Export
            plan: Export plan
            lookback: Export lookback
            lookbackDisabled: There is no lookback available yet
      activityName:
        placeholder: eg. Slab formwork
        error: Current value here is not a confirmed activity in Shape. Please click on the value to create or select an activity.
      location:
        empty: Not entered
        label: Select a location
        modal:
          title: Select a location
          cancelCTA: *actionCancel
          saveCTA: *actionSave
      organisation:
        empty: Not entered
        resourceSearchField:
          placeholder: Not entered
          createButton: Create "{term}"
          noResults: No matching search results
          error: The current value is not a confirmed resource in Shape. Please click on the value to select or create a new resource.
          organisationSuffix: organisations
      responsiblePerson:
        empty: Not entered
        search: Search for a person
      activityStatus:
        options:
          not_started: Not started
          in_progress: In progress
          completed: Completed
      planActivityStatus:
        options:
          fallback: Fallback
          planned: Planned
          additional: Additional
      requirements:
        tooltip: View requirements
        none: none
        count: "{completed} / {total}"
      schedule:
        tooltip: Select a day by clicking on it. Selected days are indigo.
        tooltipReadOnly: You cannot modify the schedule because the plan has been published.
        day1label: Sun
        day2label: Mon
        day3label: Tue
        day4label: Wed
        day5label: Thu
        day6label: Fri
        day7label: Sat
      addRowCTA: Add row
      addActivitiesCTA: Add activities
      activityDropdown:
        tooltip: Menu
        delete: Remove from plan
        viewActivity: View activity
      exportPDF:
        confirmation:
          plan:
            title: Export work plan to PDF
            subtitle: Are you sure you want to export this work plan? The PDF will be downloaded.
          lookback:
            title: Export lookback to PDF
            subtitle: Are you sure you want to export this lookback? The PDF will be downloaded.
          dismiss: *actionCancel
          confirm: Export
      addActivitiesModal:
        title: Add activities
        cancelCTA: Cancel
        addSelectedCTA: Add selected
        tooltip: Please select at least <br/> one activity to add.
        successToast: "{ numberOfActivities, plural, one {{numberOfActivities} activity has} other {{numberOfActivities} activities have}} been successfully added."
        emptyList:
          title: You don't have any activities yet.
          subtitle: Please create in the weekly work plan or import in the Activities page.
          goToActivitiesLink: Go to activities
        filtersNoResults:
          title: No matching search results
          subtitle: Try to adjust your filters or clear all to show results.
          clearAllFiltersCTA: Clear all filters
      notEditableAlertMessage: You can't make changes on this Plan tab because it has been published. To add more activities, head to the Progress & Lookback tab.
      placeholderFormLabel: New plan activity form
      prefillPlan:
        button: Prefill from previous
        modal:
          title: Override the current work plan
          subtitle:
            Activities that are "Not started" or "In progress" from <b>{planName} ({planStartDate} to {planEndDate})</b> will populate
            this plan.
          cancelCTA: Cancel
          overrideCTA: Override
        toast:
          success: Activities from {planName} were copied over successfully.
    details:
      newProgressLogCTA: New progress log
      tabs:
        editor: 1. Editor
        plan: Plan
        progressTracker: 2. Progress tracker
        lookback: 3. Lookback
        progressAndLookback: Progress & Lookback
      ppc:
        label: PPC {ppc}%
        button: PPC
        title: Percent Plan Complete (PPC)
        description: This measures how accurately planned this week's activities are. A score close to 100% indicates the plan is reliable. A low score indicates problems on site are hampering progress or overcommitment.
        calculation: 'The calculation for this is: Number of "Achieved" planned activities / Total "Planned" activities * 100.'
      draftVisibility:
        label: You only
        title: Drafts are only visible to you
        description: <p>Draft plans are editable and only visible to the author of the work plan.</p> <p>Clicking on the “Publish plan” button will make the plan uneditable. This will also make this visible to others.</p>
      taskIDVisibility:
        title: Task ID and Activity name
        description: If the activity has a Task ID, it is shown along with the activity's name. The Task ID can be added or edited in the activity's details.
      organisationMessage:
        title: Organisation
        description: Organisation options can also be found or created in shift reports or admin > resources.
      archivedPlan:
        description: This work plan has been archived.
        restoreCTA: Restore
        toast:
          success: '"{planName}" was restored. You can find it in My plans/All plans.'
    progressTracker:
      withoutUpdates: Without updates
      withUpdates: With updates
      date: Date
      editDisabled: You cannot create or edit any progress logs from here because this work plan is closed.
      emptyState:
        title: You don't have activities in this work plan to track
        description: Add activities in the Editor to start tracking progress.
      card:
        expectingProgress: Expecting {percent}% progress by {day}
        progressOnThisDay: Progress on this day
        noUpdate: No update yet
        recordProgressCTA: Record progress
        editCTA: Edit
        showPreviousCTA: Show previous
        previousProgress: Previous progress ({day})
        dropdownActions:
          workPlanActivityDetails: Work plan activity details
          delete: Delete
        deleteConfirmationModal:
          title: Delete progress log
          subtitle: Are you sure you want to delete this draft? This action cannot be undone.
          cancelCTA: *actionCancel
          deleteCTA: *actionDelete
          successToast: Progress log has been successfully deleted.
      drawer:
        title: "{isEditingLog, select, true {Edit progress log} other {Create progress log} }"
        streamliningTitle: "{isEditingLog, select, true {Edit progress} other {Record progress} }"
        subtitle: "{isEditingLog, select, true {Any change to the progress % will apply to this activity's history} other {This will be visible in the activity details and the connected weekly work plans.} }"
        cancelCTA: Cancel
        nextCTA: Next
        backCTA: Back
        doneCTA: Done
        unlinkActivityCTA: Unlink
        dateLabel: Happened on
        datePlaceholder: Select a date
        dateReadOnlyTooltip: You cannot change the date
        searchActivityLabel: "{streamlining, select, true {Activity} other {Activity from work plan} }"
        searchActivityPlaceholder: Search by name, reference or task ID
        progressLabel: "{streamlining, select, true {% Complete} other {Progress now} }"
        descriptionLabel: Description
        descriptionPlaceholder: Add a description or comment about the progress. This is optional.
        activityProgress: "{streamlining, select, true {% Complete: {percent}%} other {Progress now: {percent}%} }"
        activityReadOnlyTooltip: You cannot change the activity
        evidenceLabel: Evidence
        evidencePlaceholderTitle: No evidence yet
        createSuccessToast: The {date} progress log was successfully added in the activity "{description}"
        updateSuccessToast: Progress log has been successfully updated.
        progressLogFormFields:
          progressObservation:
            title: Progress observation
            content: Describe and measure the progress done on the linked activity.
          quantityUnits:
            quantity: Quantity
            units: Units
        shiftActivityForm:
          activityUpdates:
            title: Activity updates
            content: Based on this progress log's details, some activity details may be updated. Feel free to update any activity details below.
            streamliningContent: Update activity details considering the progress observation.
          activityDetails:
            label: Activity linked to this progress log
          actualStart:
            label: Actual start
            description: Inferred from the first progress log.
          actualFinish:
            label: Actual finish
            description: Inferred from the last progress log.
          expectedFinish:
            label: Expected finish
        alert:
          editDescription: This activity already has a progress log on {date}. Please edit that log instead or change the date.
          editCTA: Edit existing log
          connectedToWorkPlanDescription: This progress log was created in a weekly work plan.
    lookback:
      header:
        activityName: Activity name
        location: Location
        organisation: Organisation
        status: Status
        planActivityStatus: Planned?
        expectedProgressDate: Expected progress <br/> by {planEndDate}
        achievedProgressDate: Achieved progress <br/> by {planEndDate}
        progressLogs: Progress logs
        varianceCategory: Variance category
        varianceRemarks: Remarks
        varianceRecoveryMitigationMeasures: Recovery/mitigation measures
        comment: Comment
      location:
        empty: Not entered
      activityStatus:
        options:
          noStatus: No status
          notStarted: Not started
          inProgress: In progress
          completed: Completed
      planActivityStatus:
        options:
          fallback: Fallback
          planned: Planned
          additional: Additional
      achievedProgress:
        achievedBadge: Achieved
        explainVarianceCTA: Explain
      planStatus:
        closed:
          alert: The information in this tab has been locked because this work plan is closed.
        reviewing:
          alert: "<b>{ count, plural, one {{count} activity</b> is} other {{count} activities</b> are} } missing { count, plural, one {a} other {} } variance { count, plural, one {explanation} other {explanations} }"
      progressLogs:
        tooltip:
          noProgressLog: No progress log. Click to add.
          missingProgressLog: Missing progress log. Click to add.
          checkProgressLog: Click to check the progress log for this day.
          noPermissions: You don't have sufficient permissions to add a progress log.
          planClosed: You cannot add a progress log on a closed work plan.
        drawer:
          title: Progress log on {date}
      varianceCategory:
        options:
          approval_delays: Approval delays
          client_changes: Client changes
          delay_in_decision_or_communication: Delay in decision or communication
          design_information_issues: Design information issues
          documentation_issues: Documentation issues
          equipment_unavailability: Equipment unavailability
          inaccurate_estimates: Inaccurate estimates
          inadequate_access_to_workface: Inadequate access to workface
          material_shortage: Material shortage
          miscellaneous: Miscellaneous
          personnel_shortage: Personnel shortage
          previous_trade_handover_delay: Previous trade handover delay
          priority_reassessment: Priority reassessment
          process_inefficiencies: Process inefficiencies
          rework_required: Rework required
          safety_concerns_unsafe_conditions: Safety concerns/unsafe conditions
          scheduling_conflicts: Scheduling conflicts
          technical_challenges: Technical challenges
          weather_conditions: Weather conditions
      emptyState:
        title: There are no activities in your plan
        description: Add activities into the “Plan week” tab, record the progress of those activities in the “Track works” tab, and return here to compare the planned and actual activity progress.
      dayLog:
        numberOfLogs: "{numberOfLogs} / 1"
        delta: "{delta} %"
        tooltip:
          complete: "Complete: Both progress observation and % Complete are filled."
          incomplete: "Incomplete: Needs a progress observation and % Complete update."
          incomplete-delta: "Incomplete: Please add a % Complete update."
          incomplete-logs: "Incomplete: Please add a progress observation."
          not-scheduled: "No work scheduled. Click to record progress if needed."
      recordVariance:
        title: Add variance details
        saveCTA: Save
        activityFromPlan: Activity from work plan
        varianceCategory:
          label: Category
        varianceRemarks:
          label: Remarks
          placeholder: Add remarks on why the variance happened.
        varianceRecoveryMitigationMeasures:
          label: Recovery/mitigation measures
          placeholder: Add recovery/mitigation measures.
      placeholders:
        location: Not entered
        organisation: Not entered
        varianceCategory: Not entered
      additionalActivityPlaceholderRow: Additional activity row
      addAdditionalActivityCTA: Add additional activity
      additionalActivityCTAPopup: Add unexpected work that emerged during the week
    savingIndicator:
      saved: Saved
      saving: Saving

mediaGallery:
  upload:
    scope:
      document: Add document
      image: Add image
      file: Add file
    success: "{filename} successfully uploaded."
    error: "{filename} could not be uploaded. Please try again later."
    trigger:
      title: Add
    options:
      gallery:
        short_title: Gallery
        title: Upload from gallery
      camera:
        short_title: Camera
        title: Upload from camera
      documents:
        short_title: Documents
        title: Upload from device
      projectGallery:
        short_title: Project gallery
        title: Attach from project gallery
  uploadFile: Upload a file
  uploadFileDescription: Files up to 100MB
  uploadImage: Upload an image
  uploadImages: Upload images
  uploadImageDescription: JPG, PNG up to 10MB
  uploadUnsupportedOrInvalidSizeError: "{invalidFileNames, list} { invalidCount, plural, one {has} other {have} } unsupported file extension or must be greater than 0 bytes and smaller than 10MB"
  pdf:
    uploadError:
      title: We’re having difficulties opening your file
      description: Please upload this file again to properly view it
    notSupported:
      title: PDF preview is not supported in Shape's mobile view yet
      link: Open in a new tab

projectsMenu:
  newProject: New project
  allProjects: All projects

ServiceWorkerLoader:
  description: New version available
  cta: Update now

featureLimits:
  badge: Pro
  banner:
    title: This is a pro feature
    body: Unlock this and other features with a pro plan. Contact us to learn more.

historyLimits:
  cta: More info
  badge: You are seeing a limited {days} days of events.

quotaLimits:
  cta: More info
  badge: *quotaLimitsBadge

subscriptionPlan:
  modal:
    heading: Upgrade to unlock
    title: This is a pro feature
    subtitle: Unlock this and other features with a pro plan.
    learnMore: Learn more
    talkToUs: Talk to us
  page:
    printIssue: Print issue
    title: is a pro feature
    subtitle: Unlock this and other features with a pro plan.
  goBackToIssue: Go back to issue

trial:
  bar:
    badge: "{daysLeft, plural, =0  {This is the last day of your pro trial} one {{daysLeft} day left of your pro trial} other {{daysLeft} days left of your pro trial}}."
    link: "Upgrade now"
    contactUpgrade: "Please contact your administrator to upgrade."

shiftManager:
  title: Shift reports
  tabs:
    shiftReports:
      published: Published
      archived: Archived
      inProgress: In progress
    shiftActivities:
      activities: Activities
      archived: Archived activities
    manager:
      managerView: Manager view
  activities:
    emptyList:
      activities:
        title: You don't have any activities yet.
        subtitle: Add some here to quickly reference them on your shift reports.
        newCTA: New activity
      archived:
        title: You don't have any archived activities.
        subtitle: Archived activities will appear here.
    list:
      taskId: "Task ID:"
      percentageCompleted: "Progress: {progress}%"
      percentageCompletedStreamlining: "% Complete: {progress}% as of {today}"
      expectedFinish: "Expected finish: {expectedFinish}"
      archived: Archived
      critical: Critical
    noResultsPlaceholder:
      title: "No matching search results"
      subtitle: "Please check spelling or try different keywords"
    breadcrumbs:
      overview: Overview
      activityDetails: Activity details
      gallery: Gallery
      weeklyPlanning: Weekly planning
      dailyProgress: Daily progress
      readiness: Activity readiness
      resourcesUsed: Resources used
    details:
      title: Activity details
      archivedWarning: This activity is archived.
      actions:
        edit: Edit
        archive: Archive
        restore: Restore
        seeAll: See all
      shapeId: Shape ID
      taskId: Task ID
      location: Location
      status: Status
      progress: Progress
      tooltip:
        shapeId: The activity ID on Shape when this activity was created
        taskId: Used to link this activity to your master schedule (e.g. in P6, Asta, Aphex, Excel). Just make sure that this is ID is the same in your master schedule.
      timeTable:
        planned: Planned
        actual: Actual
        start: Start
        finish: Finish
        original: Original
        expected: Expected
      organisation: Organisation
      owner: Activity owner
      critical:
        title: Critical
        yes: Yes
        no: No
    gallery:
      title: Gallery
      actions:
        seeAll: See all
        sort:
          label: Sort
          newest: Newest
          oldest: Oldest
        filter:
          label: Show
          all: All
          images: Images
      emptyStateLabels:
        title: Nothing to show yet
        subtitle: Attach photos/files to progress logs or shift report progress observations.
    weeklyPlanning:
      title: Weekly planning
      actions:
        seeAll: See all
      emptyStateLabels:
        title: Nothing to show yet
        subtitle: Weekly work plans with this activity will appear once they are closed.
      tableHeaders:
        duration: Duration
        expectedProgress: Expected progress
        actualProgress: Actual progress
        varianceCategory: Variance category
        remarks: Remarks
        mitigation: Mitigation measures
        author: Author
      tooltip:
        weeklyWorkPlanLink: "Go to {planName}"
    dailyProgress:
      title: Daily progress
      emptyStateLabels:
        title: Nothing to show yet
        subtitle: Add progress logs in a weekly work plan or progress observations in a shift report.
      actions:
        seeAll: See all
      tableHeaders:
        date: Date
        location: Location
        progressDescription: Progress description
        progress: Progress
        author: Author
        planned: Planned?
    resourcesUsed:
      title: Resources used
      actions:
        seeAll: See all
      emptyStateLabels:
        title: Nothing to show yet
        subtitle: Link this activity to a resource (e.g. people, equipment, material) on a shift report.
      personnelTotalHours:
        label: Total hours worked by personnel
        tooltip: People are assigned to work on this activity. This totals every person's hours on this activity
      equipmentTotalHours:
        label: Total hours of equipment usage
        tooltip: Equipment is used to work on this activity. This totals every equipment's hours on this activity
      tableHeaders:
        material: Material
        quantity: Quantity
        unit: Unit
        date: Date
    readiness:
      title: Activity readiness
      status:
        ready: Ready
        notReady: Not ready
      updaterStatus: "<b>{teamMember}</b> marked this activity as <b>{ready, select, true {ready} other {not ready} }</b> on <b>{date}</b>"
      requirements:
        title: Requirements
        empty:
          title: What's needed to start this activity?
          description: Add task requirements to move this activity to 'Ready'.
          action: Add new requirement
        placeholder: Type a new requirement...
      createSuccess: Requirement added successfully.
      updateSuccess: Changes saved.
      updateError: Error on saving changes.
      createError: Error on creating requirement.
      deleteSuccess: Requirement successfully deleted.
      deleteError: Error on delete requirement.
      actions:
        newRequirement: New requirement
        deleteRequirement: Delete requirement
        markAsReady: Mark as ready
        markAsNotReady: Mark as not ready
        toggleReadySuccess: Activity successfully marked as "{ready, select, true {Ready} other {Not ready} }".
        toggleReadyError: Error on changing activity readiness.
        seeAll: See all
      deleteConfirmation:
        title: Delete this requirement?
        subtitle: You are about to delete a requirement. This action is permanent and cannot be undone.
        cancelCTA: *actionCancel
        deleteCTA: *actionDelete
        error: Error on deleting requirement.
        success: Requirement successfully deleted.
      blockers:
        title: Blockers
        empty:
          title: What's blocking this activity?
          description: Add issues blocking this activity from being progressed.
          action: Add blockers
        search:
          placeholder: Type to search or create new blocker
          blockerSuffix: blockers
          createButton:
            withBlockerName: Create "{blockerName}"
            emptyBlockerName: Create new blocker
          toasts:
            blankTitleError: Blocker title cannot be empty.
        actions:
          deleteBlocker: Delete blocker
          addBlockers: Add blockers
        toasts:
          addError: Error on adding blocker.
          addSuccess: Blocker added successfully.
          deleteError: Error on deleting blocker.
          deleteSuccess: Blocker successfully deleted.
        tooltips:
          noPermission:
            delete: You don't have permission to delete this blocker
            create: You don't have permission to link new blockers
        deleteConfirmation:
          title: Delete this blocker?
          subtitle: You are about to delete a blocker. This action is permanent and cannot be undone.
          cancelCTA: *actionCancel
          deleteCTA: *actionDelete
  export:
    exportModalAlert:
      all: Exporting all reports.
      filtered: Exporting reports based on current filters.
      selected: Exporting selected reports.
    xlsx:
      description: All in a single Excel file
    pdf:
      description: All as individual PDF files

activities:
  newCTA: New activity
  searchPlaceholder: Search name or reference no.
  filters:
    readiness:
      label: Readiness
      title: Filter by readiness
      options:
        all: All
        ready: Ready
        notReady: Not ready
  new:
    title: Create activity
    cancelCTA: Cancel
  edit:
    title: Edit activity
    cancelCTA: Cancel
  form:
    createCTA: Create
    saveCTA: Save changes
    fields:
      critical:
        label: Mark as critical
      description: Name
      task_identifier:
        label: Task ID
        helpText: To link this activity to your master schedule, Shape will use this ID to identify this activity. Maximum of 50 characters long.
      status:
        label: Status
        options:
          not_started: Not started
          in_progress: In progress
          completed: Completed
      location:
        label: Location
        placeholder: Add location
        form:
          title: Select a location
          cancelCTA: Cancel
          selectCTA: Select
      organisation:
        label: Organisation
        placeholder: Add organisation
      owner:
        label: Activity owner
        triggerLabel: Add owner
        filterByLabel: Filter by activity owner
        searchPlaceholder: Search for an activity owner
      percentage_completed: "Progress %"
      planned_start_date: Planned start
      planned_end_date: Planned finish
      expected_finish_date: Expected finish
      actual_start_date: Actual start
      actual_end_date: Actual finish
      progressNow:
        label: Actual progress now
        placeholder: You can only add progress when the activity has been successfully created.
        noProgress: No progress yet
        newProgressLogCTA: New progress log
        editProgressLogCTA: Edit
        connectedInfo:
          title: Connected to weekly work plans
          description: You can also change this activity's actual progress now from an “in tracking” work plan.
          disabledNewProgressTooltip: You can't create a progress log for the future. Also, an activity can only have one progress log per day.
    toast:
      createSuccess: Activity was successfully created.
      editSuccess: Activity was successfully edited.
  activityDetails:
    archivedWarning: This activity is archived.
    restoreCTA: Restore
    header:
      shapeId: "Shape ID: {id}"
      createdOn: "Created on:"
      closeCTA: Close
    status:
      title: Status
    task_identifier:
      title: Task ID
      subtitle: To link this activity to your master schedule, Shape will use this ID to identify this activity.
      placeholder: No task ID yet to link this activity to your master schedule (e.g. in P6, Asta, Aphex, Excel).
    location:
      title: Location
      notEntered: Not entered
    overview:
      title: Overview
    planned:
      title: Planned
      dates:
        plannedStart: Planned start
        plannedEnd: Planned finish
        expectedFinish: Expected finish
    actual:
      title: Actual
      dates:
        actualStart: Actual start
        actualEnd: Actual finish
      progress:
        title: Progress
        percentage: "{percentageCompleted}% as of {progressLogDate, select, undefined {} other {{progressLogDate}}} {isToday, select, true {(today)} other {}}"
        notEntered: Not entered
    date:
      notEntered: Not entered
    visibility:
      title: Visibility
      privateTitle: Private
      privateSubtitle: Only your organisation can see this activity.
    footer:
      archiveCTA: Archive
      editCTA: Edit details
      restoreCTA: Restore
    tabs:
      details: Details
      progressHistory: Progress history
  activityHistory:
    noHistory: No progress history.
    updatedProgressTo: Updated progress to
    progress: "{progress}%"
    withComment: with comment
    from: "From:"
    modified: "Modified on: {date} by {name}"
    edit: Edit
  activityCard:
    archiveCTA: Archive
    restoreCTA: Restore
  archiveModal:
    title: Archive activity
    subtitle: Are you sure you want to archive this activity?
    actions:
      cancel: Cancel
      archive: Archive
    success: The activity was archived. You can now find it in the “Archived activities” tab.
  restoreModal:
    title: Restore activity
    subtitle: Are you sure you want to restore this activity?
    actions:
      cancel: Cancel
      restore: Restore
    success: The activity was restored. You can now find it in the "Activities” tab.
  search:
    activitySuffix: activities
    createButton:
      emptyActivityName: Create activity
      withActivityName: Create "{activityName}"
  import:
    trigger:
      title: Import
    options:
      importData:
        title: Import from XLSX
        subtitle: Upload activity data using the XLSX template
        toast:
          checkingErrors: We're checking your import file for errors.
          importState: Importing activities...<br/><br/>You will receive an email summary once the import is completed. You can continue using Shape at this time.
          success: Your activities have been imported from XLSX successfully. Please check your email for the import summary.
      downloadTemplate:
        title: Download XLSX template
        subtitle: A blank template you can use to upload data
  export:
    exportCTA: Export
  title: "Activities"
  tabs:
    all: All
    archived: Archived
  blockers:
    drawer:
      title: Blockers
      subtitle: You can search, multiple select and link blockers.
      linkCTA: Link selected
      cancelCTA: Cancel
      tabs:
        all: All issues
        myIssues: My issues
      createNewIssueCTA:
        withIssueName: Create "{issueName}"
      search:
        placeholder: Type to search or create new blocker
    badge:
      noBlockers: No blockers
      unresolvedBlockersCount: "{number, plural, one {1 blocker} other {{number} blockers}}"
      resolvedBlockers: Blockers resolved

shiftReport:
  new:
    author: Author
    title: New shift report
    publish: Publish
    saveDraft: Save draft
    save: Save
    private: Private
    prefillFromPrevious: Pre-fill from previous
    editAsCollaboratorLabel: You are editing this report as a collaborator
    draftSaved: Saved
    draftSaving: Saving
    preview: Preview
  tabs:
    report: Report
    collaborators: Collaborator comms
    comments: Comments
  edit:
    title: Edit shift report
  view:
    title: View shift report
    edit: Edit
    exportCTA: Export
    archiveCTA: Archive
    restoreCTA: Restore
    shareCTA: Share
    archivedMessage: This report is archived.
    deleteCTA: *actionDelete
  list:
    table:
      you: (You)
      removedUser: (Removed user)
      date: Date
      reporter: Reporter
      team: Organisation
      visibility: Visibility
      private: Private
      public: Public
      specificTeams: Specific organisations
      shift: Shift
      author: Author
      collaborators: Collaborators
      quality: Completeness
      actions:
        header: Actions
        view: View shift report
        edit: Edit shift report
        print: Print shift report
        delete: Delete shift report
        duplicate: *actionDuplicate
        archive: *actionArchive
        restore: *actionRestore
      checkbox:
        selectAllOnPage: Select all on this page
        unselectAllOnPage: Unselect all on this page
    placeholder:
      noReports: You don't have any reports yet
      noReportsInProgress: You don't have any "in progress" shift reports
      noReportsArchived: You don't have any archived reports
      noReportsViewer: There aren't any reports yet
      info: This space will be filled with reports when your organisation starts submitting them.
      infoInProgress: Shift reports will appear for you here if you are invited to collaborate by your colleagues.
      infoViewer: You don't have permission to create reports, but this space will be filled with reports when your organisation starts submitting them.
      cta: Otherwise, you can start a new one by clicking below.
      noFiltersTitle: No reports found
      noFiltersSubtitle: There are no reports found with the filters applied. You may try adjusting the filters.
  visibility:
    private: Private
    public: Public
    specificTeams: Specific organisations
    visibility: Visibility
  qualityLabel:
    title: Completeness
    notuseful: Not useful
    thebasics: The basics
    good: Good
    verygood: Very good
    comprehensive: Comprehensive
  qualityTableInfo:
    title: Report completeness
    info:
      The report completeness indicator scores each shift report based on the presence of important information about
      the shift.
    percentageRange:
      notuseful: 0-19%
      thebasics: 20-39%
      good: 40-59%
      verygood: 60-79%
      comprehensive: ">80%"
  progressPopover:
    currentScore: Your current score
    basics:
      title: The basics
      titleInfo: "{complete} / {total}%"
      addReportDate: Add a report date
      addReportTitle: Add a report title
      addProgressDowntime: Add progress or downtime
      addWeatherDescription: Add weather description
    people:
      title: People
      titleInfo: "{complete} / {total}%"
      addPeopleToReport: Add people to your report
    equipment:
      title: Equipment
      titleInfo: "{complete} / {total}%"
      addEquipmentToReport: Add equipment to your report
    material:
      title: Material
      titleInfo: "{complete} / {total}%"
      addMaterialToReport: Add material to your report
    evidence:
      title: Evidence
      titleInfo: "{complete} / {total}%"
      reportsEvidenced: "{complete} / {total} time reports are evidenced"
    allocations:
      title: Allocations
      titleInfo: "{complete} / {total}%"
      resourcesAllocated: "{complete} / {total} resources are allocated to time"
    info:
      description: Complete the basics to unlock next steps
  approvals:
    title: Approver
    submitForApproval: Submit for approval
    pendingApproval: Pending approval
    approvePublish: Approve and publish
    inReviewTooltip: This shift report is being reviewed by the approver. If you want to publish the report without approval, you have to unassign the approver.
    successReviewToast: Shift report submitted for review

  form:
    shiftReportForm: Shift report form
    shiftReportView: Shift report view
    projectDetails: Project details
    projectName: Project name
    projectNumber: Project number
    teamName: Organisation name
    teamLogo: Organisation logo
    documents: Files & Images
    docRefNumberInternal: Document reference number (internal)
    docRefNumberClient: Document reference number (client)
    shiftDetails: Shift details
    reportTitle: Report title
    reportDate: Report date
    reportDateWarning:
      future: Future date. Ideally, report within one day of the shift.
      ideal: This is the ideal reporting timeframe, within one day of the shift.
      late: Late entry. Ideally, report within one day of the shift.
    shiftType: Shift type
    shiftStart: Shift start
    shiftEnd: Shift end
    weather: Weather
    temperature: Temperature
    description: Description
    activity: Activity
    activities: Activities
    discipline: Discipline
    location: Location
    quantity: Quantity
    progress: Progress
    units: Units
    plannedUnplanned: Planned/unplanned
    comment: Comment
    people: People
    name: Name
    role: Role
    organisation: Organisation
    hours: Total hours
    equipment: Equipment
    equipmentId: Equipment ID
    note: Note
    notes: Notes
    safetyHealthEnvironment: Safety, Health & Environment
    safetyNote: Safety note
    activityDefaultTitle: No description
    downtimeDefaultTitle: No description
    equipmentDefaultTitle: No description
    materialDefaultTitle: No description
    peopleDefaultTitle: No name
    safetyNoteDefaultTitle: No description
    material: Material
    downTime: Downtime & Delay
    issueDescription: Issue description
    timeLost: Time lost (hrs)
    reason: Reason
    addActivity: Add activity
    allocateTriggerLabel: Allocate
    unlinkToastMessage: '"{activityName}" was unlinked successfully.'
    unlinkToastUndo: Undo
    addProgress: Add progress
    allocateProgress: Allocate progress
    addPerson: Add person
    addDowntime: Add downtime
    allocateDowntime: Allocate downtime
    addEquipment: Add equipment
    addMaterial: Add material
    addSafetyNote: Add safety note
    activityDescription: Add details about the activity like description, location, progress, unit, planned status, and additional comment.
    progressDescription: Add progress observations from the shift. You can also reference activities from the Activities tab and specify what progress was made on those activities.
    downtimeDescription: Add details about the downtime like issue description, time lost and reason.
    peopleDescription: Add details about the person like name, role, organisation, total hours, and additional comment.
    peopleSubDescription: You can also link activities and downtime to each person.
    equipmentDescription: Add details about the equipment like equipment ID, quantity, and total hours.
    equipmentSubDescription: You can also link activities and downtime to each equipment.
    materialDescription: Add details about the material like description, quantity, and units.
    materialSubDescription: You can also link activities and downtime to each material.
    safetyDescription: Add additional notes specifically for safety, health, and environment.
    openRowLinks: Open row links
    closeRowLinks: Close row links
    delete: Delete
    noEntries: No entries
    upload: Upload
    change: Change
    uploadErrorMessage: The file selected has an unsupported extension or must be greater than 0 bytes and smaller than {max} MB
    activitiesBadge: "Activities ({ activityLinksNum })"
    progressBadge: "Progress ({ activityLinksNum })"
    downTimeBadge: "Downtime ({ downTimeLinksNum })"
    attachmentsBadge: "Attachments ({ attachmentsNum })"
    hoursSubTotal: Hours
    selectLocation: Select
    noLocation: No location
    locationModal:
      title: Select a location
      cancelCTA: Cancel
      saveCTA: Save
    all: All
    details: Details
    visibility:
      public: Report is public
      private: Report is private
    progressHint:
      title: What is progress?
      subtitle: Previously the “Activity” section, this is a space for you to add progress observations you have on the shift.<br/>You can now reference activities from the Activities tab and specify what progress was made on those activities.
      okCTA: Okay
    unlinkActivityCTA: Unlink
    searchActivityPlaceholder: Search name, Shape ID, or task ID
    searchIssuePlaceholder: Search an issue
    searchIssueSuffix: issues
    issue: Issue
    unlinkIssueCTA: Unlink
    createNewIssueLink: Create new issue
    linkProgress:
      removeAllActivities: Remove all
      linkActivities: Add activities
    import:
      trigger:
        title: Import
      options:
        importData:
          title: Import from XLSX
          subtitle: Upload people data using the XLSX template
          success: Shift report data has been imported from XLSX successfully
        downloadTemplate:
          title: Download XLSX template
          subtitle: A blank template you can use to upload data
      removeAll:
        title: Remove all
        success: All items in section have been removed successfully
        deleteSectionConfirmationModal:
          title: Remove all items in section
          subtitle: Are you sure you want to delete all items? This action can't be undone.
          deleteCTA: Delete
          cancelCTA: Cancel
    remove:
      title: Remove all
    attachLineItems: Attach
    fileSizeError: The file {filename} must be greater than {min} bytes and smaller than {max} MB
    attachments: Attachments
    evidence: Evidence
    resourceSearchField:
      placeholder: Search or create an option
      createButton: Create "{term}"
      noResults: No matching search results
      error: The current value is not a confirmed resource in Shape. Please click on the value to select or create a new resource.
  gallery:
    emptyPlaceholder:
      subtitle: Shift report documents will appear here as soon as they are uploaded.
  modal:
    overrideTitle: Override current form
    actionMessage: You are about to copy information from <b>{reportTitle}</b> report, including collaborators.
    warningMessage: If you have already filled information in the current report, it will be overridden.
    actions:
      cancel: Cancel
      override: Override
  exportOptionsModal:
    title: Export shift report
    contentLabel: "Choose a format:"
    actions:
      cancel: Cancel
      export: Export
  publishModal:
    title: Publish report
    subTitle: Are you sure you want to publish this report? You will not be able to edit it once published.
    actions:
      cancel: Cancel
      publish: Publish
      publishAndExport: Publish & export
  publishExportSuccess:
    title:
      export: Shift report exported
      publish: Shift report published
      publishAndExport: Shift report published and exported
    actions:
      dismiss: Dismiss
  archiveModal:
    title: Archive report
    subTitle: Are you sure you want to archive this report?
    actions:
      cancel: Cancel
      archive: *actionArchive
    feedbackMessage: The report was archived. You can now find it in the “Archived” tab.
  restoreModal:
    title: Restore report
    subTitle: Are you sure you want to restore this report?
    actions:
      cancel: Cancel
      restore: *actionRestore
    feedbackMessage: The report was restored. You can now find it in the “Published” tab.
  collaboratorsModal:
    title: Collaborators
    subTitle: Add or remove collaborators.
    addTitle: Add collaborators
    addSubTitle: Select people to add as collaborators.
    noCollaborators: No collaborators
    actions:
      add: Add
      addCollaborators: Add collaborators
      goBack: Go back
  deleteDraftModal:
    title: Delete report
    subTitle: Are you sure you want to delete this report? This action cannot be undone.
    actions:
      cancel: Cancel
      delete: *actionDelete
    feedbackMessage: The report was deleted
  collaborators:
    addCollaborators: Add collaborators
    editCollaborators: Edit collaborators
    edit: Edit
  comments:
    empty:
      title: No conversations yet
      message: This space is for you to exchange thoughts and ideas.
    delete: Delete shift report comment
    fileSizeError: The file {filename} must be greater than {min} bytes and smaller than {max} MB
    input:
      hint:
        collaborators: Only collaborators can see this comment.
        public: Everyone can see your message.
    state:
      sending: Sending...
      deleting: Deleting...
  shareModal:
    title: Share shift report
    content: Make sure whoever you shared this shift report with are able to access it. You may need speak to your project's admin to grant them access.
    shareLinkTitle: "Shape shift report: {title}"
    shareLinkContent: "Click the link to see this Shape shift report. Title:  {title}"
  unavailableErrorModal:
    title: Report unavailable
    subtitle: The report you are {action} is no longer available because it was removed by its owner.
    actions:
      dismiss: Dismiss
  managerView:
    dailyComplianceTitle: Daily compliance
    reportsList:
      emptyList: No published reports for this day
    today: Today
    actions:
      exportCTA: Export reports
      reportsSelected: "{count} reports selected"
      unselectAll: Unselect all
table:
  cursorPagination:
    showing: Showing
    of: of
    results: results
    next: Next
    previous: Previous
    resultsCount: 'Showing <span class="font-medium">{count}</span> of <span class="font-medium">{total}</span> results'
galleryPicker:
  modal:
    title: Gallery
    cancelCTA: Cancel
    addCTA: Add
    selected: "{COUNT} { COUNT, plural, =0 {file} one {file} other {files}} selected"
sharePopover:
  shareCTA: "Share"
  copyLinkCTA: "Copy link"
progressField:
  disabledTooltip: You can't edit this field

onboarding:
  pageTitle: Onboarding
  steps:
    aboutYou:
      title: Tell us about you
      currentRole:
        label: What best describes your current role?
        prefilledDescription: The person who invited you gave you this role.
      usedShapeBefore:
        label: Have you used Shape before?
      hearAboutShape:
        label: How did you hear about Shape?
    objectives:
      title: What are you hoping to achieve using Shape?
      issueTracker:
        title: Track issues
        description: Take control of our construction issues. Raise, resolve, and report construction blockers and snags in one place.
      shiftReports:
        title: Create shift reports
        description: Create professional daily site reports faster. Quickly and efficiently complete daily shift reports and send a professional PDF version to my team.
      channels:
        title: Communicate with my team
        description: Get all organisations across my projects aligned in real-time using instant messaging built for construction.
        link: Go to channels web
    joinerObjectives:
      title: Before you join {projectName}, what are you hoping to achieve using Shape?
    askCreateProject:
      title: How would you like to start?
      projectFromScratch: I want to create my own project from scratch
      sampleProject: I want to use a sample project
    createProject:
      title: Create a project
      uploadError: Failed to upload project logo
    setupTeam:
      title: Setup your organisation
      teamName:
        title: Name your organisation
        description: This is the default organisation and it's automatically created when you sign up. You can edit it and add more organisations later.
        label: Organisation name
        placeholder: Untitled organisation
      teamMembers:
        title: Invite people
        description: Add your colleagues' emails and select their permissions to send them an email invite to your project. You can add more later.
        addMore: Add more
        email:
          label: Email address
          placeholder: Email
        role:
          label: Role
        permission:
          label: Permissions
  finishOnboarding:
    title: You're all set!
    subtitle:
      projectFromScratch: Your project and organisation have been successfully created. You can now start  using Shape to collaborate, manage tasks, and achieve your goals.
      sampleProject: To help you get started, we've included a sample project, and you can later create your own project. You can now explore Shape, collaborate with your team, and achieve your goals.
    areYouInterested: Are you interested in a free intro session?
    options:
      yes:
        label: Yes! Book a free setup session
        description: A Shape representative will schedule a call with you to provide the assistance you need (e.g. answering your questions, configuring your project, providing training materials).
      no:
        label: No, I’ll explore Shape on my own for now
        description: You can book a free setup session later on.
  bookSession:
    title: Book a demo
  skipConfirmationModal:
    title: Skip onboarding
    subTitle:
      stepByStep: Onboarding includes a step-by-step guide to help you create your first project. By skipping onboarding, you'll proceed directly with a sample project instead. Are you sure you want to skip?
      projectCreation: You are currently in the project creation step. If you skip now, you will proceed with a sample project instead. Are you sure you want to skip?
    actions:
      cancel: Cancel
      yes: Yes
  actions:
    next: Next
    finish: Finish
    back: Back
    skip: Skip

productTour:
  dismissCTA: Dismiss
  dismissAllCTA: Dismiss all
  tipPulse: Show tip
  forMoreInformation: For more information, go to
  helpCenter: Help Center
  dismissConfirmationModal:
    title: Dismiss product tips?
    subTitle: If you want to go back to product tips, you can click the (?) in the top navigation.
    cancelCTA: Cancel
    dismissCTA: Dismiss
  generalPopover:
    title: Product tips
    description: Anytime you want to go back to product tips just click here.
  alert:
    message: Learn how to use {productTourName}
    viewTourCTA: View tour
  storylaneModal:
    title: Learn how to use {productTourName}
    ariaLabel: "{productTourName} tour"
  popover:
    title: Learn how to use Shape
    description: You can access the product tour anytime from the ‘Help & Tips’ page.
    gotItCTA: Got it

placeholders:
  constructionRoles: Select a construction role

controlCenter:
  pageTitle: Change tracker
  commercialTracker:
    title: Commercial tracker
    totalEntries: Total entries
    newPotentialChange: New change
    noPotentialChanges: No potential changes
    emptyStatePotentialChangesSelectionMode:
      title: No changes yet
      subtitle: Create a change to link them to change signals.
    emptyStatePotentialChanges:
      title: Track what changed and why
      subtitle: The Change tracker helps you identify and monitor changes in your project by grouping all related events, known as Change signals.
      browseSignalsCTA: Browse signals
      newChangeCTA: New change
      howItWorks: How it works
      step1:
        title: Browse signals
        description: Explore existing Change Signals in your project, such as Issues or Downtime records.
      step2:
        title: Identify changes
        description: Create a new entry in the Change Tracker table to begin monitoring updates.
      step3:
        title: Link and track
        description: Group related Change Signals into a new or existing Change to maintain clear traceability.
      learnMoreInTheHelpCenter: Learn more in the Help Center
    select: Select...
    fields:
      selectPotentialChange: Select potential change
      title: Title
      author: Author
      createdAt: Created at
      priority: Priority
      category: Responsible
      status: Status
      linkedSignals: Linked signals
      estimatedCostImpact: Estimated cost impact
      cost: Cost
      estimatedScheduleImpact: Estimated schedule impact
      workingDaysOfDelay: Working days of delay
      earlyWarningNoticeSubmitted: Formally notified
      comments: Comments
      archive: Archive
      actions: Actions
      date: Date
      impact: Impact
      shiftType: Shift type
      timeLostInHrs: Time lost (hrs)
      reason: Reason
      linkedIssue: Linked issue
      description: Description
      discipline: Discipline
      location: Location
      observedAt: Observed at
      dueDate: Due date
      plannedClosureDate: Planned closure date
      closedAt: Closed at
      delayStart: Delay start
      delayFinish: Delay finish
    loading: Loading
    viewDetails: View details
    options:
      priority:
        high: High
        medium: Medium
        low: Low
      category:
        clientIssues: Client issues
        internal: Internal
        undefined: Undefined
      status:
        clientClarificationRequested: Client clarification requested
        underInternalReview: Internal review
        awaitingClientReview: Awaiting client review
        underPreparation: Preparation
        rejectedByClient: Rejected by client
        submitted: Submitted
        unprocessed: Unprocessed
        approvedByClient: Approved by client
        canceled: Canceled
        instructionAndImplementation: Instruction & implementation
      estimatedCostImpact:
        extensive: Extensive
        major: Major
        minor: Minor
        moderate: Moderate
        none: None
        significant: Significant
      estimatedScheduleImpact:
        extendedDuration: Extended duration
        longTerm: Long term
        mediumTerm: Medium term
        minimal: Minimal
        none: None
        shortTerm: Short term
      earlyWarningNoticeSubmitted:
        yes: Yes
        no: No
    modals:
      archive:
        title: Archive Potential Change
        subtitle: Are you sure you want to archive this potential change?
        cancelCTA: *actionCancel
        archiveCTA: *actionArchive
      cancelLinkPotentialChanges:
        title: Cancel link to potential changes
        subtitle: Are you sure you want to cancel the link to potential changes?
        cancelProcessCTA: Cancel process
        backToSignalsCTA: Back to signals
      unlink:
        title: Unlink change signal
        subtitle: Are you sure you want to unlink this change signal?
        cancelCTA: *actionCancel
        unlinkCTA: *actionUnlink
      potentialChangeLinkSignalsDrawer:
        title: Linked change signals
        cancelCTA: *actionCancel
        archiveCTA: *actionArchive
        linkSignals: Link signals
        unlink: Unlink
        previous: Previous change
        next: Next change
        emptyState:
          hasNoLinkedChangeSignals: <b>"{title}"</b> has no linked change signals.
          title: No change signals linked
          subtitle: Add change signals to help better document this potential change.
          noLinks: <b>"{title}"</b> has 0 linked change signals.
        changeSignalsList:
          issue: Issue
          downtime: Downtime
          changeSignals: Change signals
          linksFor: Links for <b>"{title}"</b>
      exportChange:
        title: Export change to PDF
        subtitle: Are you sure you want to export this change? The PDF will be downloaded
        cancelCTA: *actionCancel
        exportCTA: *actionExport
    alerts:
      archiveSuccess: The potential change was successfully archived
      archiveFailed: Failed to archive the potential change
      fieldUpdateSuccess: Field updated successfully
      fieldUpdateFailed: Failed to update the field
      potentialChangeCreationFailed: Failed to create a potential change
      potentialChangeCreationSuccess: Potential change created successfully
    linkSignals: Link signals...
    selectionMode:
      message: Select one or more changes below to link to the selected signals.
      newChangeCTA: New change
      linkSelectedCTA: Link selected
      cancelCTA: *actionCancel
  changeSignals:
    title: Change signals
    fields:
      title: Title
      author: Author
      date: Date
      location: Location
    emptySelectedState:
      title: No signals selected
      subtitle: Selected change signals below
    actions:
      clearSelection: Clear selection
      link: Link
      removeSelection: Remove selection
      flagChange: Flag change
      close: Close
    changeSignalsCTA: Browse
    selectionCount: "{count} selected"
    linkStatus:
      linking:
        success: Change signals linked successfully
        failed: Failed to link change signals
      unlinking:
        success: Change signals unlinked successfully
        failed: Failed to unlink change signals
    selectChangeSignals: Select change signals
    selectedChangeSignals: Selected change signals
    linkToPotentialChanges: Link to potential changes
    types:
      issues: Issues
      downtime: Downtime
