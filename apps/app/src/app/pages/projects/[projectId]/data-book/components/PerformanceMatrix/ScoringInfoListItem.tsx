export type ScoringInfoListItemProps = {
  text: string;
  value?: string;
};
export const ScoringInfoListItem: React.FC<ScoringInfoListItemProps> = ({ text, value }) => {
  return (
    <div className="flex p-2 gap-2 justify-between items-start text-sm leading-5 font-medium">
      <span className="text-neutral">{text}</span>
      {value && <span className="text-neutral-subtlest">{value}</span>}
    </div>
  );
};
