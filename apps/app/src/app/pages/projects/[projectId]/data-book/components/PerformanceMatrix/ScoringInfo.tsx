import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { ScoringInfoListHeader } from './ScoringInfoListHeader';
import { ScoringInfoListItem } from './ScoringInfoListItem';

export const ScoringInfo: React.FC = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard.performanceDetails.issueReportsTable.scoringInfo');
  return (
    <>
      <ScoringInfoListHeader title={messages('basics.title')} />
      <ScoringInfoListItem text={messages('basics.item1')} value="10%" />
      <ScoringInfoListItem text={messages('basics.item2')} value="10%" />
      <ScoringInfoListItem text={messages('basics.item3')} value="10%" />

      <Divider orientation="horizontal" />

      <ScoringInfoListHeader title={messages('additionalDetails.title')} />
      <ScoringInfoListItem text={messages('additionalDetails.item1')} value="10%" />
      <ScoringInfoListItem text={messages('additionalDetails.item2')} value="10%" />
      <ScoringInfoListItem text={messages('additionalDetails.item3')} value="10%" />
      <ScoringInfoListItem text={messages('additionalDetails.item4')} value="5%" />
      <ScoringInfoListItem text={messages('additionalDetails.item5')} value="5%" />
      <ScoringInfoListItem text={messages('additionalDetails.item6')} value="20%" />
      <ScoringInfoListItem text={messages('additionalDetails.item7')} value="10%" />

      <Divider orientation="horizontal" />

      <ScoringInfoListHeader title={messages('notes.title')} />
      <ScoringInfoListItem text={messages('notes.item1')} />
      <ScoringInfoListItem text={messages('notes.item2')} />
    </>
  );
};
