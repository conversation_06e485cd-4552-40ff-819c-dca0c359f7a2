import React, { forwardRef } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeDetailsBasicSchema,
  ProjectSchema,
} from '@shape-construction/api/src/types';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { ArrowUpRightIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import InputCheckbox from '@shape-construction/arch-ui/src/InputCheckbox';
import Table from '@shape-construction/arch-ui/src/Table';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useUpdatePotentialChange } from 'app/queries/control-center/commercial-tracker';
import { useRowHighlight } from '../hooks/useRowHighlight';
import { Archive } from './columns/Archive';
import { Author } from './columns/Author';
import { Category } from './columns/Category';
import { Comments } from './columns/Comments';
import { Cost } from './columns/Cost';
import { CreatedAt } from './columns/CreatedAt';
import { EarlyWarningSubmitted } from './columns/EarlyWarningSubmitted';
import { EstimatedCostImpact } from './columns/EstimatedCostImpact';
import { EstimatedScheduleImpact } from './columns/EstimatedScheduleImpact';
import { LinkedSignals } from './columns/LinkedSignals';
import { Priority } from './columns/Priority';
import { Status } from './columns/Status';
import { Title } from './columns/Title';
import { WorkingDaysOfDelay } from './columns/WorkingDaysOfDelay';

const TableCell: React.FC<React.ComponentProps<'td'>> = ({ children, className, ...props }) => (
  <Table.Cell className={cn('border-[0.5px]', className)} {...props}>
    {children}
  </Table.Cell>
);

interface PotentialChangeRowProps {
  record: PotentialChangeDetailsBasicSchema;
  projectId: ProjectSchema['id'];
  titleRef?: React.RefObject<HTMLInputElement | null>;
  onViewChangeSignals: (potentialChange: PotentialChangeDetailsBasicSchema) => void;
  isSelectionMode?: boolean;
  potentialChangesToLink: Array<PotentialChangeDetailsBasicSchema['id']> | undefined;
  setPotentialChangesToLink: React.Dispatch<
    React.SetStateAction<Array<PotentialChangeDetailsBasicSchema['id']> | undefined>
  >;
  newRecordId?: PotentialChangeDetailsBasicSchema['id'];
  isActive?: boolean;
}

export const PotentialChangeRow = forwardRef<HTMLTableRowElement, PotentialChangeRowProps>(
  (
    {
      record,
      projectId,
      titleRef,
      onViewChangeSignals,
      isSelectionMode,
      potentialChangesToLink,
      setPotentialChangesToLink,
      newRecordId,
      isActive = false,
    },
    ref
  ) => {
    const project = useCurrentProject();
    const messages = useMessageGetter('controlCenter.commercialTracker');
    const highlightAsNewRecord = useRowHighlight(newRecordId, record.id);

    const { mutate: updatePotentialChange } = useUpdatePotentialChange();

    const onUpdatePotentialChangeRecord = (
      values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
    ) => {
      updatePotentialChange({
        potentialChangeId: record.id,
        projectId: projectId,
        data: values,
      });
    };

    const onOpenLinkedSignalsDrawer = () => {
      onViewChangeSignals(record);
    };

    const onPotentialChangeSelect = () => {
      if (potentialChangesToLink?.includes(record.id)) {
        setPotentialChangesToLink((prev) => prev?.filter((id) => id !== record.id));
      } else {
        setPotentialChangesToLink((prev) => [...(prev || []), record.id]);
      }
    };

    return (
      <Table.Row
        ref={ref}
        striped
        className="group/change-row border-b hover:bg-neutral-subtlest-hovered transition-colors duration-1000"
        highlighted={highlightAsNewRecord || isActive}
        highlightColor={highlightAsNewRecord ? 'success' : 'primary'}
      >
        {isSelectionMode && (
          <TableCell className="pl-4 sm:first:pl-8">
            <InputCheckbox
              aria-label={messages('fields.selectPotentialChange')}
              name={`potentialChange-${record.id}`}
              checked={!!potentialChangesToLink?.includes(record.id)}
              onChange={onPotentialChangeSelect}
            />
          </TableCell>
        )}
        <TableCell>
          <div className="flex items-center gap-2">
            <Title record={record} inputRef={titleRef} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
            <div className="flex opacity-0 group-hover/change-row:opacity-100 transition-opacity">
              <IconButton
                aria-label={messages('viewDetails')}
                shape="square"
                color="secondary"
                size="xxs"
                variant="outlined"
                icon={ArrowUpRightIcon}
                onClick={onOpenLinkedSignalsDrawer}
              />
            </div>
          </div>
        </TableCell>

        <TableCell>
          <Priority record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell>
          <Category record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell>
          <Status record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell>
          <EstimatedCostImpact record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell>
          <Cost record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell>
          <EstimatedScheduleImpact record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell>
          <WorkingDaysOfDelay record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell>
          <EarlyWarningSubmitted record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell>
          <Author record={record} />
        </TableCell>

        <TableCell>
          <CreatedAt record={record} project={project} />
        </TableCell>

        <TableCell aria-label={messages('fields.linkedSignals')} onClick={onOpenLinkedSignalsDrawer}>
          <LinkedSignals record={record} />
        </TableCell>

        <TableCell>
          <Comments record={record} onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} />
        </TableCell>

        <TableCell align="right" className="sticky right-0 border-l-4 bg-inherit">
          <Archive record={record} potentialChangeId={record.id} />
        </TableCell>
      </Table.Row>
    );
  }
);
