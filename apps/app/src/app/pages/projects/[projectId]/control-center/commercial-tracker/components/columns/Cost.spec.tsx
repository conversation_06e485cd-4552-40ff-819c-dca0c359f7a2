import React from 'react';
import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { render, screen, userEvent } from 'tests/test-utils';
import { Cost } from './Cost';

describe('<Cost />', () => {
  const mockOnUpdatePotentialChangeRecord = jest.fn();
  const potentialChange = potentialChangeFactory();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders with initial cost value', () => {
    render(
      <Cost
        record={{ ...potentialChange, cost: '$1,000' }}
        onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
      />
    );

    expect(screen.getByRole('textbox')).toHaveValue('$1,000');
  });

  it('calls onUpdatePotentialChangeRecord on blur with updated value', async () => {
    render(<Cost record={potentialChange} onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord} />);

    await userEvent.clear(screen.getByRole('textbox'));
    await userEvent.type(screen.getByRole('textbox'), '$5,500');
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).toHaveBeenCalledWith({ cost: '$5,500' });
  });

  it('handles empty initial cost', () => {
    render(
      <Cost
        record={{ ...potentialChange, cost: null }}
        onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
      />
    );

    expect(screen.getByRole('textbox')).toHaveValue('');
  });

  it('trims whitespace before updating', async () => {
    render(<Cost record={potentialChange} onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord} />);

    await userEvent.clear(screen.getByRole('textbox'));
    await userEvent.type(screen.getByRole('textbox'), '  $2,500  ');
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).toHaveBeenCalledWith({ cost: '$2,500' });
  });

  it('does not call onUpdatePotentialChangeRecord when value is unchanged', async () => {
    render(
      <Cost
        record={{ ...potentialChange, cost: '$1,000' }}
        onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
      />
    );

    await userEvent.click(screen.getByRole('textbox'));
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).not.toHaveBeenCalled();
  });
});
