import { useCallback } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import InputCheckbox from '@shape-construction/arch-ui/src/InputCheckbox';
import Table from '@shape-construction/arch-ui/src/Table';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { formatDate } from '@shape-construction/utils/DateTime';
import { InfiniteLoadWaypoints } from 'app/components/InfiniteLoadWaypoints/InfiniteLoadWaypoints';
import { Link } from 'app/components/UI/Link/Link';
import { truncatedLocationPath } from 'app/components/Utils/locations';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useProjectLocations } from 'app/queries/projects/locations';
import { useProjectPeople } from 'app/queries/projects/people';
import type { ChangeSignalListItem } from '../../commercial-tracker/types';

type ChangeSignalsTableProps = {
  changeSignals: ChangeSignalListItem[];
  disabledChangeSignals?: ChangeSignalListItem[];
  selectedSignals: ChangeSignalListItem[];
  onSelectionChange: (selectedSignals: ChangeSignalListItem[]) => void;
  onLoadMore: () => void;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
};

export function getSignalLink(signal: ChangeSignalListItem, projectId: ProjectSchema['id']): string {
  switch (signal.signalType) {
    case 'issue':
      return `/projects/${projectId}/issues/lists/all?issueId=${signal.signalId}`;
    case 'downtime':
      return `/projects/${projectId}/shift-reports/${signal.shiftReportId}`;
    default:
      return '#';
  }
}

export const ChangeSignalsTable: React.FC<ChangeSignalsTableProps> = ({
  changeSignals,
  disabledChangeSignals,
  selectedSignals,
  onSelectionChange,
  onLoadMore,
  hasNextPage,
  isFetchingNextPage,
}) => {
  const project = useCurrentProject();
  const { data: projectPeople } = useProjectPeople(project.id);
  const { data: locations } = useProjectLocations(project.id);
  const messages = useMessageGetter('controlCenter.changeSignals');
  const loadingMessage = useMessage('controlCenter.commercialTracker.loading');

  const isSignalSelected = useCallback(
    (id: string) => selectedSignals.some((signal) => signal.id === id),
    [selectedSignals]
  );

  const handleSelectSignal = useCallback(
    (signal: ChangeSignalListItem) => {
      if (isSignalSelected(signal.id)) {
        onSelectionChange(selectedSignals.filter((s) => s.id !== signal.id));
      } else {
        onSelectionChange([...selectedSignals, signal]);
      }
    },
    [isSignalSelected, onSelectionChange, selectedSignals]
  );
  return (
    <Table.Container aria-label={messages('title')} className="md:rounded-none grow ring-0 border-b-2 overflow-auto">
      <Table className="max-h-96 border-separate border-spacing-0" rowDensity="condensed">
        <Table.Heading className="sticky top-0 z-10">
          <Table.Row className="bg-neutral-subtle">
            <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle" />
            <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle">
              <span className="py-1">{messages('fields.title')}</span>
            </Table.Header>
            <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle">
              <span className="py-1">{messages('fields.author')}</span>
            </Table.Header>
            <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle">
              <span className="py-1">{messages('fields.date')}</span>
            </Table.Header>
            <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle">
              <span className="py-1">{messages('fields.location')}</span>
            </Table.Header>
          </Table.Row>
        </Table.Heading>
        <Table.Body>
          {changeSignals?.map((signal) => {
            const { id, title, locationId, publishedAt, teamMemberId } = signal;
            const teamMember = projectPeople?.find((person) => person.id === teamMemberId);
            const isDisabled = disabledChangeSignals?.some((disabledSignal) => disabledSignal.id === id);
            return (
              <Table.Row
                striped
                key={id}
                className={cn('border-b hover:bg-neutral-subtlest-hovered', { 'opacity-50': isDisabled })}
              >
                <Table.Cell className="border-b">
                  <InputCheckbox
                    name={`signal-${id}`}
                    checked={isSignalSelected(id)}
                    onChange={() => handleSelectSignal(signal)}
                    disabled={isDisabled}
                  />
                </Table.Cell>
                <Table.Cell className="border-b">
                  {isDisabled ? (
                    <span className="text-xs leading-5 font-medium text-link-brand underline">{title}</span>
                  ) : (
                    <Link
                      target="_blank"
                      className="text-xs leading-5 font-medium text-link-brand cursor-pointer underline"
                      to={getSignalLink(signal, project.id)}
                    >
                      {title}
                    </Link>
                  )}
                </Table.Cell>
                <Table.Cell className="border-b">
                  <div className="flex gap-2 items-center" aria-label={messages('fields.author')}>
                    <UserAvatar user={teamMember?.user} size="xs" />
                    <span className="text-neutral-bold text-xs leading-5 font-normal">{teamMember?.user.name}</span>
                  </div>
                </Table.Cell>
                <Table.Cell className="border-b">
                  <span className="text-neutral-bold text-xs leading-5 font-normal">
                    {formatDate(publishedAt, project.timezone, 'DD-MMM-YYYY')}
                  </span>
                </Table.Cell>
                <Table.Cell className="border-b">
                  <span className="text-neutral-bold text-xs leading-5 font-normal">
                    {truncatedLocationPath(locations, locationId)}
                  </span>
                </Table.Cell>
              </Table.Row>
            );
          })}
        </Table.Body>
      </Table>
      <InfiniteLoadWaypoints hasNextPage={hasNextPage} fetchNext={onLoadMore} isLast>
        {!isFetchingNextPage && hasNextPage && (
          <div className="flex w-full items-center justify-center">
            <span className="text-neutral-subtle">{loadingMessage}</span>
          </div>
        )}
      </InfiniteLoadWaypoints>
    </Table.Container>
  );
};
