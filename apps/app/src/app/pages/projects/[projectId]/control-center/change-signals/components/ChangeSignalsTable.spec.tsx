import {
  changeSignalsDowntimesFactory,
  changeSignalsIssuesFactory,
} from '@shape-construction/api/factories/control-center';
import { locationFactory } from '@shape-construction/api/factories/locations';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { getApiProjectsMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler } from '@shape-construction/api/handlers-factories/projects/control-center';
import { getApiProjectsProjectIdLocationsMockHandler } from '@shape-construction/api/handlers-factories/projects/locations';
import type { LocationSchema } from '@shape-construction/api/src/types';
import { server } from 'tests/mock-server';
import { render, screen, userEvent } from 'tests/test-utils';
import { ChangeSignalsTable } from './ChangeSignalsTable';

describe('<ChangeSignalsTable />', () => {
  const locations: LocationSchema[] = [
    locationFactory({
      id: 'loc-1',
      name: 'Project wide',
      shortCode: 'RAI',
      parentLocationId: null,
    }),
    locationFactory({
      id: 'loc-2',
      name: 'KGS',
      shortCode: 'KGS',
      parentLocationId: 'loc-1',
    }),
    locationFactory({
      id: 'loc-3',
      name: 'Level 1',
      shortCode: 'LEV1',
      parentLocationId: 'loc-2',
    }),
  ];

  beforeEach(() => {
    server.use(
      getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
      getApiProjectsProjectIdLocationsMockHandler(() => locations)
    );
  });

  it('renders the table with correct column headers', async () => {
    render(
      <ChangeSignalsTable
        changeSignals={[changeSignalsIssuesFactory()]}
        selectedSignals={[]}
        onSelectionChange={() => {}}
        hasNextPage
        isFetchingNextPage
        onLoadMore={() => {}}
      />
    );

    await screen.findByRole('table');

    expect(screen.getByText('controlCenter.changeSignals.fields.title')).toBeInTheDocument();
    expect(screen.getByText('controlCenter.changeSignals.fields.author')).toBeInTheDocument();
    expect(screen.getByText('controlCenter.changeSignals.fields.date')).toBeInTheDocument();
    expect(screen.getByText('controlCenter.changeSignals.fields.location')).toBeInTheDocument();
  });

  it('displays change signal data correctly in the table', async () => {
    const date = '2023-10-01T12:00:00Z';
    render(
      <ChangeSignalsTable
        changeSignals={[
          changeSignalsIssuesFactory({
            title: 'change signal issue',
            locationId: 'loc-3',
            publishedAt: date,
          }),
        ]}
        selectedSignals={[]}
        onSelectionChange={() => {}}
        hasNextPage
        isFetchingNextPage
        onLoadMore={() => {}}
      />
    );

    expect(await screen.findByText('change signal issue')).toBeInTheDocument();
    expect(await screen.findByText('01-Oct-2023')).toBeInTheDocument();
    expect(await screen.findByText('RAI > KGS > LEV1')).toBeInTheDocument();
  });

  it('allows selecting a change signal', async () => {
    const changeSignal = changeSignalsIssuesFactory({
      signalId: 'signal-1',
      title: 'change signal issue',
    });
    const onSelectionChangeMock = jest.fn();
    render(
      <ChangeSignalsTable
        changeSignals={[changeSignal]}
        selectedSignals={[]}
        onSelectionChange={onSelectionChangeMock}
        hasNextPage
        isFetchingNextPage
        onLoadMore={() => {}}
      />
    );

    const checkbox = await screen.findByRole('checkbox');
    await userEvent.click(checkbox);

    expect(onSelectionChangeMock).toHaveBeenCalledWith([changeSignal]);
  });

  it('allows deselecting a change signal', async () => {
    const changeSignal = changeSignalsIssuesFactory({
      signalId: 'signal-1',
      title: 'change signal issue',
    });
    const onSelectionChangeMock = jest.fn();
    render(
      <ChangeSignalsTable
        changeSignals={[changeSignal]}
        selectedSignals={[changeSignal]}
        onSelectionChange={onSelectionChangeMock}
        hasNextPage
        isFetchingNextPage
        onLoadMore={() => {}}
      />
    );

    const checkbox = await screen.findByRole('checkbox');
    expect(checkbox).toBeChecked();
    await userEvent.click(checkbox);

    expect(onSelectionChangeMock).toHaveBeenCalledWith([]);
  });

  it('shows selected signals as checked', async () => {
    const changeSignals = [
      changeSignalsIssuesFactory({
        signalId: 'signal-1',
        title: 'change signal issue 1',
      }),
      changeSignalsIssuesFactory({
        signalId: 'signal-2',
        title: 'change signal issue 2',
      }),
    ];
    render(
      <ChangeSignalsTable
        changeSignals={changeSignals}
        selectedSignals={[changeSignals[1]]}
        onSelectionChange={() => {}}
        hasNextPage
        isFetchingNextPage
        onLoadMore={() => {}}
      />
    );

    const checkboxes = await screen.findAllByRole('checkbox');

    expect(checkboxes[0]).not.toBeChecked();
    expect(checkboxes[1]).toBeChecked();
  });

  it('disables checkboxes for disabled change signals', async () => {
    const changeSignals = [
      changeSignalsIssuesFactory({
        signalId: 'signal-1',
        title: 'change signal issue 1',
      }),
      changeSignalsIssuesFactory({
        signalId: 'signal-2',
        title: 'change signal issue 2',
      }),
    ];

    render(
      <ChangeSignalsTable
        changeSignals={changeSignals}
        disabledChangeSignals={[changeSignals[1]]}
        selectedSignals={[]}
        onSelectionChange={() => {}}
        hasNextPage
        isFetchingNextPage
        onLoadMore={() => {}}
      />
    );
    const checkboxes = await screen.findAllByRole('checkbox');

    expect(checkboxes[0]).toBeEnabled();
    expect(checkboxes[1]).toBeDisabled();
  });

  it('calls onLoadMore when scrolling to bottom with hasNextPage', async () => {
    const onLoadMoreMock = jest.fn();
    render(
      <ChangeSignalsTable
        changeSignals={[changeSignalsIssuesFactory()]}
        selectedSignals={[]}
        onSelectionChange={() => {}}
        hasNextPage
        isFetchingNextPage={false}
        onLoadMore={onLoadMoreMock}
      />
    );

    const container = await screen.findByRole('table');
    container.scrollTop = container.scrollHeight;
    container.dispatchEvent(new Event('scroll'));

    expect(onLoadMoreMock).toHaveBeenCalled();
  });

  it('does not call onLoadMore when scrolling to bottom without hasNextPage', async () => {
    const onLoadMoreMock = jest.fn();
    render(
      <ChangeSignalsTable
        changeSignals={[changeSignalsIssuesFactory()]}
        selectedSignals={[]}
        onSelectionChange={() => {}}
        hasNextPage={false}
        isFetchingNextPage={false}
        onLoadMore={onLoadMoreMock}
      />
    );

    const container = await screen.findByRole('table');
    container.scrollTop = container.scrollHeight;
    container.dispatchEvent(new Event('scroll'));

    expect(onLoadMoreMock).not.toHaveBeenCalled();
  });

  describe('link generation based on signal type', () => {
    describe('when signal type is issue', () => {
      it('renders a link that navigates to issues', async () => {
        const project = projectFactory({ id: 'project-0' });
        server.use(getApiProjectsMockHandler(() => [project]));
        const issueSignal = changeSignalsIssuesFactory({
          signalId: 'issue-123',
          signalType: 'issue',
          title: 'Test Issue Signal',
        });

        render(
          <ChangeSignalsTable
            changeSignals={[issueSignal]}
            selectedSignals={[]}
            onSelectionChange={() => {}}
            hasNextPage={false}
            isFetchingNextPage={false}
            onLoadMore={() => {}}
          />
        );

        expect(await screen.findByRole('link', { name: 'Test Issue Signal' })).toHaveAttribute(
          'href',
          '/projects/project-0/issues/lists/all?issueId=issue-123'
        );
      });
    });

    describe('when signal type is downtime', () => {
      it('renders a link that navigates to shift reports', async () => {
        const project = projectFactory({ id: 'project-0' });
        server.use(getApiProjectsMockHandler(() => [project]));
        const downtimeSignal = changeSignalsDowntimesFactory({
          signalId: 'downtime-456',
          signalType: 'downtime',
          shiftReportId: 'shift-report-789',
          title: 'Test Downtime Signal',
        });

        render(
          <ChangeSignalsTable
            changeSignals={[downtimeSignal]}
            selectedSignals={[]}
            onSelectionChange={() => {}}
            hasNextPage={false}
            isFetchingNextPage={false}
            onLoadMore={() => {}}
          />
        );

        expect(await screen.findByRole('link', { name: 'Test Downtime Signal' })).toHaveAttribute(
          'href',
          '/projects/project-0/shift-reports/shift-report-789'
        );
      });
    });
  });
});
