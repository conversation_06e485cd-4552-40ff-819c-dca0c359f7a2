import React from 'react';
import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { render, screen } from 'tests/test-utils';
import { PotentialChangesTable } from './PotentialChangesTable';

describe('<PotentialChangesTable />', () => {
  it('displays the table with correct headers', async () => {
    const potentialChanges = [potentialChangeFactory(), potentialChangeFactory()];

    render(
      <PotentialChangesTable
        fetchNextPage={jest.fn()}
        hasNextPage={false}
        isFetchingNextPage={false}
        isSelectionMode={false}
        onViewChangeSignals={jest.fn()}
        potentialChanges={potentialChanges}
        selectedPotentialChangeIds={undefined}
        setSelectedPotentialChangeIds={jest.fn()}
        titleRef={React.createRef<HTMLInputElement>()}
      />
    );

    expect(await screen.findByText('controlCenter.commercialTracker.fields.title')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.createdAt')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.author')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.priority')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.category')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.status')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.estimatedCostImpact')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.cost')).toBeInTheDocument();
    expect(
      await screen.findByText('controlCenter.commercialTracker.fields.estimatedScheduleImpact')
    ).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.workingDaysOfDelay')).toBeInTheDocument();
    expect(
      await screen.findByText('controlCenter.commercialTracker.fields.earlyWarningNoticeSubmitted')
    ).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.comments')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.actions')).toBeInTheDocument();
  });
});
